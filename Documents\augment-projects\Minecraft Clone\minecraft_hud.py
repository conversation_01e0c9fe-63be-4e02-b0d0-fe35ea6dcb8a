"""
HUD and UI rendering for the Minecraft clone.

This module handles 2D overlay rendering including crosshair,
hotbar, debug info, and other UI elements.
"""

import pygame
import numpy as np
from OpenGL.GL import *
from typing import List, Tuple, Optional

from config import (
    WINDOW_WIDTH, WINDOW_HEIGHT, HOTBAR_SLOTS,
    BlockType, BLOCK_PROPERTIES
)
from math_utils import Vec3
from player import Player
from renderer import RenderStats


class HUD:
    """Handles all 2D HUD rendering."""
    
    def __init__(self):
        """Initialize HUD system."""
        # Font for text rendering
        pygame.font.init()
        self.font_small = pygame.font.Font(None, 16)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_large = pygame.font.Font(None, 32)
        
        # Colors
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        self.YELLOW = (255, 255, 0)
        self.GRAY = (128, 128, 128)
        self.DARK_GRAY = (64, 64, 64)
        
        # UI element sizes
        self.crosshair_size = 20
        self.hotbar_slot_size = 48
        self.hotbar_padding = 4
        
        # Debug info visibility
        self.show_fps = True
        self.show_position = True
        self.show_chunk_info = True
    
    def render(self, player: Player, fps: float, render_stats: RenderStats,
               world_stats: dict, debug_mode: bool = False):
        """
        Render all HUD elements.
        
        Args:
            player: Player object
            fps: Current FPS
            render_stats: Rendering statistics
            world_stats: World statistics
            debug_mode: Whether to show debug info
        """
        # Switch to 2D rendering
        self._begin_2d()
        
        # Always visible elements
        self._render_crosshair()
        self._render_hotbar(player)
        
        # Debug elements
        if debug_mode:
            self._render_debug_info(player, fps, render_stats, world_stats)
        elif self.show_fps:
            self._render_fps(fps)
        
        # Switch back to 3D
        self._end_2d()
    
    def _begin_2d(self):
        """Set up OpenGL for 2D rendering."""
        # Save matrices
        glMatrixMode(GL_PROJECTION)
        glPushMatrix()
        glLoadIdentity()
        glOrtho(0, WINDOW_WIDTH, WINDOW_HEIGHT, 0, -1, 1)
        
        glMatrixMode(GL_MODELVIEW)
        glPushMatrix()
        glLoadIdentity()
        
        # Disable depth testing for 2D
        glDisable(GL_DEPTH_TEST)
        glDisable(GL_TEXTURE_2D)
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
    
    def _end_2d(self):
        """Restore OpenGL 3D settings."""
        # Restore matrices
        glMatrixMode(GL_PROJECTION)
        glPopMatrix()
        glMatrixMode(GL_MODELVIEW)
        glPopMatrix()
        
        # Re-enable depth testing
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_TEXTURE_2D)
    
    def _render_crosshair(self):
        """Render the center crosshair."""
        center_x = WINDOW_WIDTH // 2
        center_y = WINDOW_HEIGHT // 2
        half_size = self.crosshair_size // 2
        thickness = 2
        
        # Set color
        glColor4f(1.0, 1.0, 1.0, 0.8)
        
        # Draw horizontal line
        glBegin(GL_QUADS)
        glVertex2f(center_x - half_size, center_y - thickness // 2)
        glVertex2f(center_x + half_size, center_y - thickness // 2)
        glVertex2f(center_x + half_size, center_y + thickness // 2)
        glVertex2f(center_x - half_size, center_y + thickness // 2)
        glEnd()
        
        # Draw vertical line
        glBegin(GL_QUADS)
        glVertex2f(center_x - thickness // 2, center_y - half_size)
        glVertex2f(center_x + thickness // 2, center_y - half_size)
        glVertex2f(center_x + thickness // 2, center_y + half_size)
        glVertex2f(center_x - thickness // 2, center_y + half_size)
        glEnd()
    
    def _render_hotbar(self, player: Player):
        """Render the hotbar with items."""
        # Calculate hotbar dimensions
        total_width = (self.hotbar_slot_size * HOTBAR_SLOTS + 
                      self.hotbar_padding * (HOTBAR_SLOTS - 1))
        start_x = (WINDOW_WIDTH - total_width) // 2
        start_y = WINDOW_HEIGHT - self.hotbar_slot_size - 20
        
        # Draw hotbar slots
        for i in range(HOTBAR_SLOTS):
            x = start_x + i * (self.hotbar_slot_size + self.hotbar_padding)
            y = start_y
            
            # Draw slot background
            if i == player.selected_slot:
                glColor4f(1.0, 1.0, 1.0, 0.8)
            else:
                glColor4f(0.3, 0.3, 0.3, 0.7)
            
            self._draw_rect(x, y, self.hotbar_slot_size, self.hotbar_slot_size)
            
            # Draw slot border
            glColor4f(0.0, 0.0, 0.0, 1.0)
            self._draw_rect_outline(x, y, self.hotbar_slot_size, 
                                  self.hotbar_slot_size, 2)
            
            # Draw item in slot
            if i < len(player.hotbar):
                block_type = player.hotbar[i]
                if block_type != BlockType.AIR:
                    self._render_block_icon(x + 4, y + 4,
                                          self.hotbar_slot_size - 8,
                                          block_type)
            
            # Draw slot number
            self._render_text(str(i + 1), x + 2, y + 2,
                            self.font_small, self.WHITE)
    
    def _render_block_icon(self, x: int, y: int, size: int, 
                          block_type: BlockType):
        """Render a block icon in the UI."""
        # Get block color (simplified representation)
        block_colors = {
            BlockType.STONE: (0.5, 0.5, 0.5, 1.0),
            BlockType.DIRT: (0.54, 0.35, 0.17, 1.0),
            BlockType.GRASS: (0.34, 0.49, 0.27, 1.0),
            BlockType.SAND: (0.93, 0.79, 0.67, 1.0),
            BlockType.WATER: (0.25, 0.64, 0.87, 0.8),
            BlockType.WOOD: (0.63, 0.32, 0.18, 1.0),
            BlockType.LEAVES: (0.0, 0.5, 0.0, 0.9),
            BlockType.COBBLESTONE: (0.39, 0.39, 0.39, 1.0),
            BlockType.PLANKS: (0.77, 0.64, 0.42, 1.0),
            BlockType.GLASS: (1.0, 1.0, 1.0, 0.3),
        }
        
        color = block_colors.get(block_type, (0.5, 0.5, 0.5, 1.0))
        glColor4f(*color)
        
        # Draw block face
        self._draw_rect(x, y, size, size)
        
        # Draw shading for 3D effect
        glColor4f(0.0, 0.0, 0.0, 0.3)
        glBegin(GL_TRIANGLES)
        glVertex2f(x, y + size)
        glVertex2f(x, y)
        glVertex2f(x + size, y + size)
        glEnd()
        
        glColor4f(1.0, 1.0, 1.0, 0.2)
        glBegin(GL_TRIANGLES)
        glVertex2f(x, y)
        glVertex2f(x + size, y)
        glVertex2f(x + size, y + size)
        glEnd()
    
    def _render_debug_info(self, player: Player, fps: float,
                          render_stats: RenderStats, world_stats: dict):
        """Render debug information overlay."""
        y_offset = 10
        line_height = 18
        x_pos = 10
        
        # FPS
        self._render_text(f"FPS: {fps:.1f}", x_pos, y_offset,
                         self.font_medium, self.YELLOW)
        y_offset += line_height
        
        # Position
        pos = player.position
        self._render_text(f"XYZ: {pos.x:.1f} / {pos.y:.1f} / {pos.z:.1f}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        # Rotation
        rot = player.rotation
        self._render_text(f"Rotation: {np.degrees(rot.y):.1f}° / {np.degrees(rot.x):.1f}°",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        # Chunk position
        chunk_x = int(pos.x // 16)
        chunk_y = int(pos.y // 16)
        chunk_z = int(pos.z // 16)
        self._render_text(f"Chunk: {chunk_x} / {chunk_y} / {chunk_z}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        y_offset += 10  # Extra spacing
        
        # World statistics
        self._render_text(f"Chunks Loaded: {world_stats.get('chunks_loaded', 0)}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        self._render_text(f"Chunks Generating: {world_stats.get('chunks_generating', 0)}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        self._render_text(f"Memory: {world_stats.get('memory_usage_mb', 0):.1f} MB",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        y_offset += 10  # Extra spacing
        
        # Render statistics
        self._render_text(f"Chunks Rendered: {render_stats.chunks_rendered}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        self._render_text(f"Chunks Culled: {render_stats.chunks_culled}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        self._render_text(f"Triangles: {render_stats.triangles_rendered:,}",
                         x_pos, y_offset, self.font_small, self.WHITE)
        y_offset += line_height
        
        # Player state
        y_offset += 10
        states = []
        if player.is_flying:
            states.append("Flying")
        if player.is_sprinting:
            states.append("Sprinting")
        if player.is_crouching:
            states.append("Crouching")
        if player.on_ground:
            states.append("On Ground")
        
        if states:
            self._render_text(f"State: {', '.join(states)}",
                             x_pos, y_offset, self.font_small, self.GREEN)
    
    def _render_fps(self, fps: float):
        """Render just the FPS counter."""
        self._render_text(f"FPS: {fps:.1f}", 10, 10,
                         self.font_medium, self.YELLOW)
    
    def _render_text(self, text: str, x: int, y: int,
                    font: pygame.font.Font, color: Tuple[int, int, int]):
        """Render text to screen using pygame."""
        # Create text surface
        text_surface = font.render(text, True, color)
        text_data = pygame.image.tostring(text_surface, "RGBA", True)
        
        # Get dimensions
        width = text_surface.get_width()
        height = text_surface.get_height()
        
        # Create texture
        texture = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, texture)
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0,
                    GL_RGBA, GL_UNSIGNED_BYTE, text_data)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        
        # Render textured quad
        glEnable(GL_TEXTURE_2D)
        glBindTexture(GL_TEXTURE_2D, texture)
        glColor4f(1.0, 1.0, 1.0, 1.0)
        
        glBegin(GL_QUADS)
        glTexCoord2f(0, 0); glVertex2f(x, y)
        glTexCoord2f(1, 0); glVertex2f(x + width, y)
        glTexCoord2f(1, 1); glVertex2f(x + width, y + height)
        glTexCoord2f(0, 1); glVertex2f(x, y + height)
        glEnd()
        
        # Cleanup
        glDeleteTextures(1, [texture])
        glDisable(GL_TEXTURE_2D)
    
    def _draw_rect(self, x: int, y: int, width: int, height: int):
        """Draw a filled rectangle."""
        glBegin(GL_QUADS)
        glVertex2f(x, y)
        glVertex2f(x + width, y)
        glVertex2f(x + width, y + height)
        glVertex2f(x, y + height)
        glEnd()
    
    def _draw_rect_outline(self, x: int, y: int, width: int, height: int,
                          thickness: int = 1):
        """Draw a rectangle outline."""
        # Top
        self._draw_rect(x, y, width, thickness)
        # Bottom
        self._draw_rect(x, y + height - thickness, width, thickness)
        # Left
        self._draw_rect(x, y, thickness, height)
        # Right
        self._draw_rect(x + width - thickness, y, thickness, height)