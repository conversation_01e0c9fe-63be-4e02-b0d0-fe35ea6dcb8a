"""
Configuration and constants for the Minecraft clone.

This module contains all game-wide configuration settings, constants,
and enumerations used throughout the project.
"""

from enum import Enum, auto
from dataclasses import dataclass
from typing import Tuple
import numpy as np


# Window and display settings
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
TARGET_FPS = 60
FOV = 70  # Field of view in degrees
NEAR_PLANE = 0.1
FAR_PLANE = 1000.0

# World dimensions
WORLD_WIDTH = 1000
WORLD_HEIGHT = 100
WORLD_DEPTH = 1000

# Chunk settings
CHUNK_SIZE = 16  # 16x16x16 blocks per chunk
CHUNKS_X = WORLD_WIDTH // CHUNK_SIZE
CHUNKS_Y = WORLD_HEIGHT // CHUNK_SIZE
CHUNKS_Z = WORLD_DEPTH // CHUNK_SIZE

# Rendering settings
RENDER_DISTANCE = 8  # Chunks in each direction
LOD_DISTANCES = [2, 4, 8, 16]  # Chunk distances for different LOD levels
FRUSTUM_CULLING_ENABLED = True
OCCLUSION_CULLING_ENABLED = True
GREEDY_MESHING_ENABLED = True

# Physics settings
GRAVITY = -9.8  # m/s²
PLAYER_HEIGHT = 1.8
PLAYER_WIDTH = 0.6
PLAYER_EYE_HEIGHT = 1.65
JUMP_VELOCITY = 5.0
WALK_SPEED = 4.3
SPRINT_SPEED = 6.0
FRICTION = 0.91
AIR_RESISTANCE = 0.98

# Block types
class BlockType(Enum):
    """Enumeration of all block types in the game."""
    AIR = 0
    STONE = 1
    DIRT = 2
    GRASS = 3
    SAND = 4
    WATER = 5
    WOOD = 6
    LEAVES = 7
    BEDROCK = 8
    COAL_ORE = 9
    IRON_ORE = 10
    GOLD_ORE = 11
    DIAMOND_ORE = 12
    COBBLESTONE = 13
    PLANKS = 14
    GLASS = 15


@dataclass
class BlockProperties:
    """Properties for each block type."""
    name: str
    texture_id: int
    is_solid: bool
    is_transparent: bool
    is_liquid: bool
    hardness: float
    light_emission: int = 0
    stack_size: int = 64


# Block properties database
BLOCK_PROPERTIES = {
    BlockType.AIR: BlockProperties("Air", 0, False, True, False, 0.0),
    BlockType.STONE: BlockProperties("Stone", 1, True, False, False, 1.5),
    BlockType.DIRT: BlockProperties("Dirt", 2, True, False, False, 0.5),
    BlockType.GRASS: BlockProperties("Grass", 3, True, False, False, 0.6),
    BlockType.SAND: BlockProperties("Sand", 4, True, False, False, 0.5),
    BlockType.WATER: BlockProperties("Water", 5, False, True, True, 100.0),
    BlockType.WOOD: BlockProperties("Wood", 6, True, False, False, 2.0),
    BlockType.LEAVES: BlockProperties("Leaves", 7, True, True, False, 0.2),
    BlockType.BEDROCK: BlockProperties("Bedrock", 8, True, False, False, -1.0),
    BlockType.COAL_ORE: BlockProperties("Coal Ore", 9, True, False, False, 3.0),
    BlockType.IRON_ORE: BlockProperties("Iron Ore", 10, True, False, False, 3.0),
    BlockType.GOLD_ORE: BlockProperties("Gold Ore", 11, True, False, False, 3.0),
    BlockType.DIAMOND_ORE: BlockProperties("Diamond Ore", 12, True, False, False, 3.0),
    BlockType.COBBLESTONE: BlockProperties("Cobblestone", 13, True, False, False, 2.0),
    BlockType.PLANKS: BlockProperties("Planks", 14, True, False, False, 2.0),
    BlockType.GLASS: BlockProperties("Glass", 15, True, True, False, 0.3),
}

# Terrain generation settings
TERRAIN_SCALE = 0.005  # Frequency for terrain noise
TERRAIN_OCTAVES = 6
TERRAIN_PERSISTENCE = 0.5
TERRAIN_LACUNARITY = 2.0
SEA_LEVEL = 32
MAX_HEIGHT = 80

# Cave generation settings
CAVE_THRESHOLD = 0.45
CAVE_SCALE = 0.03
CAVE_OCTAVES = 3

# Biome settings
BIOME_SCALE = 0.001
MOISTURE_SCALE = 0.002

# Day/night cycle settings
DAY_LENGTH_SECONDS = 600  # 10 minutes real time
SUNRISE_TIME = 0.25  # 6 AM game time
SUNSET_TIME = 0.75   # 6 PM game time

# Inventory settings
INVENTORY_SLOTS = 36
HOTBAR_SLOTS = 9

# Colors (RGB)
SKY_DAY_COLOR = (135, 206, 235)
SKY_NIGHT_COLOR = (10, 10, 30)
SKY_SUNRISE_COLOR = (255, 192, 128)
SKY_SUNSET_COLOR = (255, 128, 64)

# Input settings
MOUSE_SENSITIVITY = 0.002
SCROLL_SENSITIVITY = 1.0

# Performance settings
MAX_CHUNKS_LOADED = 1000
CHUNK_LOAD_PER_FRAME = 2
CHUNK_UNLOAD_DISTANCE = RENDER_DISTANCE + 2
MULTIPROCESSING_ENABLED = True
WORKER_PROCESSES = 4

# File paths
SAVE_DIRECTORY = "saves"
TEXTURE_DIRECTORY = "assets/textures"
SHADER_DIRECTORY = "assets/shaders"

# Debug settings
DEBUG_MODE = False
SHOW_FPS = True
SHOW_COORDINATES = True
WIREFRAME_MODE = False

# Vertex format for rendering
VERTEX_FORMAT = np.dtype([
    ('position', np.float32, 3),
    ('texture', np.float32, 2),
    ('normal', np.float32, 3),
    ('lighting', np.float32, 1),
])

# Face directions and normals
class Face(Enum):
    """Block face directions."""
    TOP = 0
    BOTTOM = 1
    NORTH = 2
    SOUTH = 3
    EAST = 4
    WEST = 5


# Face normals for lighting calculations
FACE_NORMALS = {
    Face.TOP: np.array([0, 1, 0], dtype=np.float32),
    Face.BOTTOM: np.array([0, -1, 0], dtype=np.float32),
    Face.NORTH: np.array([0, 0, -1], dtype=np.float32),
    Face.SOUTH: np.array([0, 0, 1], dtype=np.float32),
    Face.EAST: np.array([1, 0, 0], dtype=np.float32),
    Face.WEST: np.array([-1, 0, 0], dtype=np.float32),
}

# Vertex offsets for each face (for cube construction)
FACE_VERTICES = {
    Face.TOP: [
        [0, 1, 0], [1, 1, 0], [1, 1, 1], [0, 1, 1]
    ],
    Face.BOTTOM: [
        [0, 0, 0], [0, 0, 1], [1, 0, 1], [1, 0, 0]
    ],
    Face.NORTH: [
        [0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0]
    ],
    Face.SOUTH: [
        [1, 0, 1], [0, 0, 1], [0, 1, 1], [1, 1, 1]
    ],
    Face.EAST: [
        [1, 0, 0], [1, 0, 1], [1, 1, 1], [1, 1, 0]
    ],
    Face.WEST: [
        [0, 0, 1], [0, 0, 0], [0, 1, 0], [0, 1, 1]
    ],
}

# Texture coordinates for faces
FACE_TEXTURE_COORDS = [
    [0, 0], [1, 0], [1, 1], [0, 1]
]

# Neighbor offsets for block adjacency checks
NEIGHBOR_OFFSETS = [
    (0, 1, 0),   # Top
    (0, -1, 0),  # Bottom
    (0, 0, -1),  # North
    (0, 0, 1),   # South
    (1, 0, 0),   # East
    (-1, 0, 0),  # West
]


def get_block_property(block_type: BlockType, property_name: str):
    """
    Get a specific property of a block type.
    
    Args:
        block_type: The type of block
        property_name: Name of the property to retrieve
        
    Returns:
        The requested property value
        
    Raises:
        KeyError: If the block type or property doesn't exist
    """
    if block_type not in BLOCK_PROPERTIES:
        raise KeyError(f"Unknown block type: {block_type}")
    
    properties = BLOCK_PROPERTIES[block_type]
    if not hasattr(properties, property_name):
        raise KeyError(f"Block type {block_type} has no property '{property_name}'")
    
    return getattr(properties, property_name)


def is_transparent(block_type: BlockType) -> bool:
    """Check if a block type is transparent."""
    return BLOCK_PROPERTIES[block_type].is_transparent


def is_solid(block_type: BlockType) -> bool:
    """Check if a block type is solid."""
    return BLOCK_PROPERTIES[block_type].is_solid


def get_texture_id(block_type: BlockType) -> int:
    """Get the texture ID for a block type."""
    return BLOCK_PROPERTIES[block_type].texture_id