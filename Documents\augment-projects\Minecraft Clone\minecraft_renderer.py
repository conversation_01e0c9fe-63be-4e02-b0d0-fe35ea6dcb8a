"""
Rendering system for the Minecraft clone using PyOpenGL.

This module handles all 3D rendering including chunk meshes,
frustum culling, and shader management.
"""

import numpy as np
import pygame
from pygame.locals import *
from OpenGL.GL import *
from OpenGL.GLU import *
from OpenGL.GL import shaders
from typing import List, Dict, Tuple, Optional
import os
from dataclasses import dataclass

from minecraft_config import (
    WINDOW_WIDTH, WINDOW_HEIGHT, FOV, NEAR_PLANE, FAR_PLANE,
    VERTEX_FORMAT, Face, FACE_VERTICES, FACE_TEXTURE_COORDS,
    FACE_NORMALS, BlockType, BLOCK_PROPERTIES, RENDER_DISTANCE,
    SKY_DAY_COLOR, SKY_NIGHT_COLOR, CHUNK_SIZE
)
from minecraft_math_utils import (
    Vec3, create_perspective_matrix, create_view_matrix,
    create_rotation_matrix, Frustum
)
from minecraft_chunk import Chunk, ChunkState


@dataclass
class RenderStats:
    """Statistics for rendering performance."""
    chunks_rendered: int = 0
    chunks_culled: int = 0
    triangles_rendered: int = 0
    draw_calls: int = 0
    frame_time_ms: float = 0.0


class Shader:
    """Manages OpenGL shader programs."""
    
    def __init__(self, vertex_source: str, fragment_source: str):
        """
        Create shader program from source code.
        
        Args:
            vertex_source: Vertex shader source code
            fragment_source: Fragment shader source code
        """
        # Compile shaders
        vertex_shader = shaders.compileShader(vertex_source, GL_VERTEX_SHADER)
        fragment_shader = shaders.compileShader(fragment_source, GL_FRAGMENT_SHADER)
        
        # Create program
        self.program = shaders.compileProgram(vertex_shader, fragment_shader)
        
        # Cache uniform locations
        self.uniforms = {}
        
        # Clean up shaders (they're linked to program now)
        glDeleteShader(vertex_shader)
        glDeleteShader(fragment_shader)
    
    def use(self):
        """Activate this shader program."""
        glUseProgram(self.program)
    
    def get_uniform_location(self, name: str) -> int:
        """Get cached uniform location."""
        if name not in self.uniforms:
            self.uniforms[name] = glGetUniformLocation(self.program, name)
        return self.uniforms[name]
    
    def set_mat4(self, name: str, matrix: np.ndarray):
        """Set a 4x4 matrix uniform."""
        loc = self.get_uniform_location(name)
        if loc != -1:
            glUniformMatrix4fv(loc, 1, GL_FALSE, matrix.astype(np.float32))
    
    def set_vec3(self, name: str, vec: Vec3):
        """Set a vec3 uniform."""
        loc = self.get_uniform_location(name)
        if loc != -1:
            glUniform3f(loc, vec.x, vec.y, vec.z)
    
    def set_float(self, name: str, value: float):
        """Set a float uniform."""
        loc = self.get_uniform_location(name)
        if loc != -1:
            glUniform1f(loc, value)
    
    def set_int(self, name: str, value: int):
        """Set an int uniform."""
        loc = self.get_uniform_location(name)
        if loc != -1:
            glUniform1i(loc, value)


# Default shaders
VERTEX_SHADER_SOURCE = """
#version 330 core

layout(location = 0) in vec3 aPos;
layout(location = 1) in vec2 aTexCoord;
layout(location = 2) in vec3 aNormal;
layout(location = 3) in float aLighting;

out vec2 TexCoord;
out vec3 Normal;
out vec3 FragPos;
out float Lighting;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

void main()
{
    FragPos = vec3(model * vec4(aPos, 1.0));
    gl_Position = projection * view * vec4(FragPos, 1.0);
    TexCoord = aTexCoord;
    Normal = mat3(transpose(inverse(model))) * aNormal;
    Lighting = aLighting;
}
"""

FRAGMENT_SHADER_SOURCE = """
#version 330 core

in vec2 TexCoord;
in vec3 Normal;
in vec3 FragPos;
in float Lighting;

out vec4 FragColor;

uniform sampler2D texture1;
uniform vec3 lightDir;
uniform vec3 viewPos;
uniform float ambientStrength;
uniform vec3 fogColor;
uniform float fogDensity;

void main()
{
    // Texture sampling
    vec4 texColor = texture(texture1, TexCoord);
    if(texColor.a < 0.1)
        discard;
    
    // Ambient lighting
    vec3 ambient = ambientStrength * vec3(1.0);
    
    // Diffuse lighting
    vec3 norm = normalize(Normal);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * vec3(1.0);
    
    // Combine lighting
    vec3 result = (ambient + diffuse * 0.5) * texColor.rgb * Lighting;
    
    // Fog calculation
    float distance = length(viewPos - FragPos);
    float fogFactor = exp(-fogDensity * distance);
    fogFactor = clamp(fogFactor, 0.0, 1.0);
    
    result = mix(fogColor, result, fogFactor);
    
    FragColor = vec4(result, texColor.a);
}
"""


class ChunkMesh:
    """Represents the mesh data for a chunk."""
    
    def __init__(self):
        """Initialize empty mesh."""
        self.vao = 0
        self.vbo = 0
        self.vertex_count = 0
        self.is_empty = True
    
    def build_from_chunk(self, chunk: Chunk):
        """
        Build mesh from chunk data using greedy meshing.
        
        Args:
            chunk: Chunk to build mesh from
        """
        # Get visible faces
        visible_faces = chunk.get_visible_faces()
        
        if not visible_faces:
            self.is_empty = True
            return
        
        # Apply greedy meshing
        if hasattr(chunk, 'greedy_meshing_enabled') and chunk.greedy_meshing_enabled:
            merged_faces = self._greedy_mesh(chunk, visible_faces)
        else:
            merged_faces = [(f[0], f[1], f[2], f[3], f[4], 1, 1) 
                          for f in visible_faces]
        
        # Build vertex data
        vertices = []
        
        for x, y, z, face, block_type, width, height in merged_faces:
            # Get block properties
            texture_id = BLOCK_PROPERTIES[block_type].texture_id
            
            # Get face vertices and modify for merged faces
            face_verts = FACE_VERTICES[face]
            face_normal = FACE_NORMALS[face]
            
            # Calculate lighting based on face
            lighting = self._calculate_face_lighting(face)
            
            # Generate vertices for this face
            for i, (vx, vy, vz) in enumerate(face_verts):
                # Adjust vertex position for merged faces
                if face in [Face.TOP, Face.BOTTOM]:
                    vert_x = x + vx * width
                    vert_y = y + vy
                    vert_z = z + vz * height
                elif face in [Face.NORTH, Face.SOUTH]:
                    vert_x = x + vx * width
                    vert_y = y + vy * height
                    vert_z = z + vz
                else:  # EAST, WEST
                    vert_x = x + vx
                    vert_y = y + vy * height
                    vert_z = z + vz * width
                
                # Texture coordinates
                tex_u, tex_v = FACE_TEXTURE_COORDS[i]
                
                # Add vertex
                vertices.extend([
                    chunk.world_x + vert_x,
                    chunk.world_y + vert_y,
                    chunk.world_z + vert_z,
                    tex_u, tex_v,
                    face_normal[0], face_normal[1], face_normal[2],
                    lighting
                ])
        
        # Convert to numpy array
        vertex_data = np.array(vertices, dtype=np.float32)
        
        # Update chunk vertex data
        chunk.vertex_data = vertex_data
        chunk.vertex_count = len(vertex_data) // 9  # 9 floats per vertex
        chunk.face_count = len(merged_faces)
        
        # Create OpenGL objects
        self._create_gl_objects(vertex_data)
        
        self.is_empty = False
        self.vertex_count = chunk.vertex_count
    
    def _greedy_mesh(self, chunk: Chunk, visible_faces: List) -> List:
        """
        Apply greedy meshing algorithm to merge adjacent faces.
        
        Args:
            chunk: Chunk being meshed
            visible_faces: List of visible faces
            
        Returns:
            List of merged faces (x, y, z, face, block_type, width, height)
        """
        # Group faces by direction and block type
        face_groups = {}
        for x, y, z, face, block_type in visible_faces:
            key = (face, block_type)
            if key not in face_groups:
                face_groups[key] = []
            face_groups[key].append((x, y, z))
        
        merged_faces = []
        
        # Process each group
        for (face, block_type), positions in face_groups.items():
            # Create 2D grid for this face
            if face in [Face.TOP, Face.BOTTOM]:
                # XZ plane
                grid = {}
                for x, y, z in positions:
                    grid[(x, z)] = y
                merged = self._merge_2d_grid(grid, face, block_type, 0, 2)
            elif face in [Face.NORTH, Face.SOUTH]:
                # XY plane
                grid = {}
                for x, y, z in positions:
                    grid[(x, y)] = z
                merged = self._merge_2d_grid(grid, face, block_type, 0, 1)
            else:  # EAST, WEST
                # YZ plane
                grid = {}
                for x, y, z in positions:
                    grid[(y, z)] = x
                merged = self._merge_2d_grid(grid, face, block_type, 1, 2)
            
            merged_faces.extend(merged)
        
        return merged_faces
    
    def _merge_2d_grid(self, grid: Dict, face: Face, block_type: BlockType,
                      axis1: int, axis2: int) -> List:
        """Merge a 2D grid of faces."""
        if not grid:
            return []
        
        merged = []
        visited = set()
        
        for (u, v), w in grid.items():
            if (u, v) in visited:
                continue
            
            # Find rectangle starting at this position
            width = 1
            height = 1
            
            # Extend width
            while (u + width, v) in grid and (u + width, v) not in visited:
                if grid[(u + width, v)] == w:
                    width += 1
                else:
                    break
            
            # Extend height
            can_extend = True
            while can_extend:
                for du in range(width):
                    if ((u + du, v + height) not in grid or
                        (u + du, v + height) in visited or
                        grid[(u + du, v + height)] != w):
                        can_extend = False
                        break
                
                if can_extend:
                    height += 1
            
            # Mark as visited
            for du in range(width):
                for dv in range(height):
                    visited.add((u + du, v + dv))
            
            # Add merged face
            if axis1 == 0 and axis2 == 2:  # XZ plane
                merged.append((u, w, v, face, block_type, width, height))
            elif axis1 == 0 and axis2 == 1:  # XY plane
                merged.append((u, v, w, face, block_type, width, height))
            else:  # YZ plane
                merged.append((w, u, v, face, block_type, height, width))
        
        return merged
    
    def _calculate_face_lighting(self, face: Face) -> float:
        """Calculate lighting for a face."""
        # Simple directional lighting based on face
        lighting_values = {
            Face.TOP: 1.0,
            Face.BOTTOM: 0.5,
            Face.NORTH: 0.8,
            Face.SOUTH: 0.8,
            Face.EAST: 0.6,
            Face.WEST: 0.6,
        }
        return lighting_values.get(face, 0.8)
    
    def _create_gl_objects(self, vertex_data: np.ndarray):
        """Create OpenGL VAO and VBO."""
        # Generate VAO and VBO
        self.vao = glGenVertexArrays(1)
        self.vbo = glGenBuffers(1)
        
        # Bind VAO
        glBindVertexArray(self.vao)
        
        # Bind and fill VBO
        glBindBuffer(GL_ARRAY_BUFFER, self.vbo)
        glBufferData(GL_ARRAY_BUFFER, vertex_data.nbytes, vertex_data, GL_STATIC_DRAW)
        
        # Configure vertex attributes
        stride = 9 * 4  # 9 floats * 4 bytes per float
        
        # Position attribute
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, stride, ctypes.c_void_p(0))
        glEnableVertexAttribArray(0)
        
        # Texture coordinate attribute
        glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, stride, ctypes.c_void_p(12))
        glEnableVertexAttribArray(1)
        
        # Normal attribute
        glVertexAttribPointer(2, 3, GL_FLOAT, GL_FALSE, stride, ctypes.c_void_p(20))
        glEnableVertexAttribArray(2)
        
        # Lighting attribute
        glVertexAttribPointer(3, 1, GL_FLOAT, GL_FALSE, stride, ctypes.c_void_p(32))
        glEnableVertexAttribArray(3)
        
        # Unbind
        glBindBuffer(GL_ARRAY_BUFFER, 0)
        glBindVertexArray(0)
    
    def render(self):
        """Render this mesh."""
        if self.is_empty or self.vertex_count == 0:
            return
        
        glBindVertexArray(self.vao)
        glDrawArrays(GL_QUADS, 0, self.vertex_count)
        glBindVertexArray(0)
    
    def cleanup(self):
        """Clean up OpenGL resources."""
        if self.vbo:
            glDeleteBuffers(1, [self.vbo])
        if self.vao:
            glDeleteVertexArrays(1, [self.vao])


class Renderer:
    """Main rendering system."""
    
    def __init__(self):
        """Initialize the renderer."""
        self.screen = None
        self.shader = None
        self.chunk_meshes: Dict[Tuple[int, int, int], ChunkMesh] = {}
        self.texture_atlas = None
        self.frustum = Frustum()
        self.render_stats = RenderStats()
        
        # Camera properties
        self.camera_pos = Vec3(0, 80, 0)
        self.camera_rot = Vec3(0, 0, 0)  # pitch, yaw, roll
        
        # Matrices
        self.projection_matrix = None
        self.view_matrix = None
        
        # Rendering settings
        self.fog_density = 0.01
        self.render_distance = RENDER_DISTANCE
        self.wireframe = False
    
    def initialize(self):
        """Initialize Pygame and OpenGL."""
        # Initialize Pygame
        pygame.init()
        pygame.display.set_caption("Minecraft Clone")
        
        # Set OpenGL attributes
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_PROFILE_MASK,
                                      pygame.GL_CONTEXT_PROFILE_CORE)
        
        # Create window
        self.screen = pygame.display.set_mode(
            (WINDOW_WIDTH, WINDOW_HEIGHT),
            DOUBLEBUF | OPENGL
        )
        
        # Initialize OpenGL
        self._init_gl()
        
        # Create shader
        self.shader = Shader(VERTEX_SHADER_SOURCE, FRAGMENT_SHADER_SOURCE)
        
        # Create projection matrix
        aspect = WINDOW_WIDTH / WINDOW_HEIGHT
        self.projection_matrix = create_perspective_matrix(FOV, aspect, 
                                                         NEAR_PLANE, FAR_PLANE)
        
        # Load textures
        self._load_textures()
    
    def _init_gl(self):
        """Initialize OpenGL settings."""
        # Enable depth testing
        glEnable(GL_DEPTH_TEST)
        glDepthFunc(GL_LESS)
        
        # Enable face culling
        glEnable(GL_CULL_FACE)
        glCullFace(GL_BACK)
        glFrontFace(GL_CCW)
        
        # Enable blending for transparency
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
        
        # Set clear color
        glClearColor(0.529, 0.807, 0.922, 1.0)  # Sky blue
        
        # Enable vsync (if available)
        try:
            pygame.display.gl_set_swap_interval(1)
        except AttributeError:
            # Fallback for older pygame versions
            pass
    
    def _load_textures(self):
        """Load block textures."""
        # For now, create a simple colored texture atlas
        # In a full implementation, this would load actual texture files
        atlas_size = 256
        block_size = 16
        
        # Create texture atlas array
        atlas_data = np.zeros((atlas_size, atlas_size, 4), dtype=np.uint8)
        
        # Define simple colors for each block type
        block_colors = {
            BlockType.STONE: (128, 128, 128, 255),
            BlockType.DIRT: (139, 90, 43, 255),
            BlockType.GRASS: (86, 125, 70, 255),
            BlockType.SAND: (238, 203, 173, 255),
            BlockType.WATER: (64, 164, 223, 128),
            BlockType.WOOD: (160, 82, 45, 255),
            BlockType.LEAVES: (0, 128, 0, 200),
            BlockType.BEDROCK: (51, 51, 51, 255),
            BlockType.COAL_ORE: (64, 64, 64, 255),
            BlockType.IRON_ORE: (183, 183, 183, 255),
            BlockType.GOLD_ORE: (255, 215, 0, 255),
            BlockType.DIAMOND_ORE: (185, 242, 255, 255),
            BlockType.COBBLESTONE: (100, 100, 100, 255),
            BlockType.PLANKS: (196, 164, 108, 255),
            BlockType.GLASS: (255, 255, 255, 64),
        }
        
        # Fill atlas with block colors
        for block_type, color in block_colors.items():
            texture_id = BLOCK_PROPERTIES[block_type].texture_id
            row = texture_id // (atlas_size // block_size)
            col = texture_id % (atlas_size // block_size)
            
            y_start = row * block_size
            x_start = col * block_size
            
            atlas_data[y_start:y_start + block_size,
                      x_start:x_start + block_size] = color
        
        # Create OpenGL texture
        self.texture_atlas = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, self.texture_atlas)
        
        # Set texture parameters
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT)
        
        # Upload texture data
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, atlas_size, atlas_size, 0,
                    GL_RGBA, GL_UNSIGNED_BYTE, atlas_data)
        
        glBindTexture(GL_TEXTURE_2D, 0)
    
    def update_camera(self, position: Vec3, rotation: Vec3):
        """Update camera position and rotation."""
        self.camera_pos = position
        self.camera_rot = rotation
        
        # Update view matrix
        forward = Vec3(
            np.sin(rotation.y) * np.cos(rotation.x),
            np.sin(rotation.x),
            -np.cos(rotation.y) * np.cos(rotation.x)
        )
        target = position + forward
        up = Vec3(0, 1, 0)
        
        self.view_matrix = create_view_matrix(position, target, up)
        
        # Update frustum
        mvp = np.dot(self.projection_matrix, self.view_matrix)
        self.frustum.update_from_matrix(mvp)
    
    def create_chunk_mesh(self, chunk: Chunk):
        """Create or update mesh for a chunk."""
        key = (chunk.chunk_x, chunk.chunk_y, chunk.chunk_z)
        
        if key not in self.chunk_meshes:
            self.chunk_meshes[key] = ChunkMesh()
        
        mesh = self.chunk_meshes[key]
        mesh.build_from_chunk(chunk)
        chunk.state = ChunkState.READY
        chunk.is_dirty = False
    
    def remove_chunk_mesh(self, chunk_x: int, chunk_y: int, chunk_z: int):
        """Remove mesh for a chunk."""
        key = (chunk_x, chunk_y, chunk_z)
        if key in self.chunk_meshes:
            self.chunk_meshes[key].cleanup()
            del self.chunk_meshes[key]
    
    def render(self, chunks: List[Chunk], time_of_day: float):
        """
        Render the scene.
        
        Args:
            chunks: List of chunks to render
            time_of_day: Time of day (0-1)
        """
        # Clear screen
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        
        # Reset stats
        self.render_stats = RenderStats()
        
        # Use shader
        self.shader.use()
        
        # Set uniforms
        self.shader.set_mat4("projection", self.projection_matrix)
        self.shader.set_mat4("view", self.view_matrix)
        self.shader.set_vec3("viewPos", self.camera_pos)
        
        # Calculate sun direction based on time
        sun_angle = time_of_day * 2 * np.pi
        sun_dir = Vec3(
            np.cos(sun_angle),
            np.sin(sun_angle),
            0.2
        ).normalize()
        self.shader.set_vec3("lightDir", sun_dir)
        
        # Set ambient lighting based on time
        ambient = 0.3 + 0.4 * np.sin(sun_angle)
        self.shader.set_float("ambientStrength", max(0.2, ambient))
        
        # Set fog
        fog_color = self._calculate_fog_color(time_of_day)
        self.shader.set_vec3("fogColor", 
                            Vec3(fog_color[0], fog_color[1], fog_color[2]))
        self.shader.set_float("fogDensity", self.fog_density)
        
        # Bind texture
        glActiveTexture(GL_TEXTURE0)
        glBindTexture(GL_TEXTURE_2D, self.texture_atlas)
        self.shader.set_int("texture1", 0)
        
        # Set polygon mode
        if self.wireframe:
            glPolygonMode(GL_FRONT_AND_BACK, GL_LINE)
        else:
            glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
        
        # Sort chunks by distance for transparency
        sorted_chunks = sorted(chunks, 
                             key=lambda c: self._chunk_distance_squared(c),
                             reverse=True)
        
        # Render chunks
        for chunk in sorted_chunks:
            if self._should_render_chunk(chunk):
                self._render_chunk(chunk)
        
        # Reset polygon mode
        glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
        
        # Swap buffers
        pygame.display.flip()
    
    def _should_render_chunk(self, chunk: Chunk) -> bool:
        """Check if chunk should be rendered."""
        # Check if ready
        if chunk.state != ChunkState.READY:
            return False
        
        # Check if empty
        if chunk.is_empty:
            return False
        
        # Frustum culling
        if not self.frustum.contains_aabb(chunk.get_bounding_box()):
            self.render_stats.chunks_culled += 1
            return False
        
        # Distance culling
        if self._chunk_distance_squared(chunk) > (self.render_distance * CHUNK_SIZE) ** 2:
            self.render_stats.chunks_culled += 1
            return False
        
        return True
    
    def _render_chunk(self, chunk: Chunk):
        """Render a single chunk."""
        # Check if mesh needs update
        if chunk.is_dirty:
            self.create_chunk_mesh(chunk)
        
        # Get mesh
        key = (chunk.chunk_x, chunk.chunk_y, chunk.chunk_z)
        mesh = self.chunk_meshes.get(key)
        
        if not mesh or mesh.is_empty:
            return
        
        # Set model matrix (identity for now, chunks are already in world space)
        model_matrix = np.identity(4, dtype=np.float32)
        self.shader.set_mat4("model", model_matrix)
        
        # Render mesh
        mesh.render()
        
        # Update stats
        self.render_stats.chunks_rendered += 1
        self.render_stats.triangles_rendered += chunk.face_count * 2
        self.render_stats.draw_calls += 1
    
    def _chunk_distance_squared(self, chunk: Chunk) -> float:
        """Calculate squared distance from camera to chunk center."""
        center = Vec3(
            chunk.world_x + CHUNK_SIZE / 2,
            chunk.world_y + CHUNK_SIZE / 2,
            chunk.world_z + CHUNK_SIZE / 2
        )
        diff = center - self.camera_pos
        return diff.length_squared()
    
    def _calculate_fog_color(self, time_of_day: float) -> Tuple[float, float, float]:
        """Calculate fog color based on time of day."""
        # Interpolate between day and night colors
        day_color = np.array(SKY_DAY_COLOR) / 255.0
        night_color = np.array(SKY_NIGHT_COLOR) / 255.0
        
        # Simple day/night interpolation
        t = (np.cos(time_of_day * 2 * np.pi) + 1) / 2
        color = day_color * t + night_color * (1 - t)
        
        return tuple(color)
    
    def cleanup(self):
        """Clean up resources."""
        # Clean up meshes
        for mesh in self.chunk_meshes.values():
            mesh.cleanup()
        
        # Delete texture
        if self.texture_atlas:
            glDeleteTextures(1, [self.texture_atlas])
        
        # Delete shader
        if self.shader:
            glDeleteProgram(self.shader.program)
        
        # Quit pygame
        pygame.quit()