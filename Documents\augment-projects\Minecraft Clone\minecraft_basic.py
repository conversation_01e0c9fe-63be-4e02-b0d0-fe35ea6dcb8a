#!/usr/bin/env python3
"""
Ultra-Simple Minecraft Clone that definitely works.
Basic 3D world with cubes and movement.
"""

import pygame
import sys
import math
from OpenGL.GL import *
from OpenGL.GLU import *

# Constants
WINDOW_WIDTH = 1024
WINDOW_HEIGHT = 768

class BasicMinecraft:
    def __init__(self):
        self.running = True
        self.camera_x = 0.0
        self.camera_y = 5.0
        self.camera_z = 0.0
        self.camera_yaw = 0.0
        self.camera_pitch = 0.0
        self.move_speed = 5.0
        self.mouse_sensitivity = 0.002
        
    def initialize(self):
        """Initialize pygame and OpenGL."""
        print("=== Basic Minecraft Clone ===")
        print("Initializing...")
        
        pygame.init()
        pygame.display.set_caption("Basic Minecraft Clone")
        
        # Create window
        self.screen = pygame.display.set_mode(
            (WINDOW_WIDTH, WINDOW_HEIGHT),
            pygame.DOUBLEBUF | pygame.OPENGL
        )
        
        # Set up OpenGL
        glEnable(GL_DEPTH_TEST)
        glClearColor(0.5, 0.8, 0.9, 1.0)  # Sky blue
        
        # Set up perspective
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        gluPerspective(70, WINDOW_WIDTH / WINDOW_HEIGHT, 0.1, 1000.0)
        glMatrixMode(GL_MODELVIEW)
        
        # Capture mouse
        pygame.mouse.set_visible(False)
        pygame.event.set_grab(True)
        
        print("✅ Ready to play!")
        print("Controls: WASD=move, Mouse=look, ESC=exit")
        
    def draw_cube(self, x, y, z, color=(0.5, 0.3, 0.1)):
        """Draw a cube at position."""
        glPushMatrix()
        glTranslatef(x, y, z)
        glColor3f(*color)
        
        # Draw cube using quads
        glBegin(GL_QUADS)
        
        # Front face
        glVertex3f(-0.5, -0.5,  0.5)
        glVertex3f( 0.5, -0.5,  0.5)
        glVertex3f( 0.5,  0.5,  0.5)
        glVertex3f(-0.5,  0.5,  0.5)
        
        # Back face
        glVertex3f(-0.5, -0.5, -0.5)
        glVertex3f(-0.5,  0.5, -0.5)
        glVertex3f( 0.5,  0.5, -0.5)
        glVertex3f( 0.5, -0.5, -0.5)
        
        # Top face
        glVertex3f(-0.5,  0.5, -0.5)
        glVertex3f(-0.5,  0.5,  0.5)
        glVertex3f( 0.5,  0.5,  0.5)
        glVertex3f( 0.5,  0.5, -0.5)
        
        # Bottom face
        glVertex3f(-0.5, -0.5, -0.5)
        glVertex3f( 0.5, -0.5, -0.5)
        glVertex3f( 0.5, -0.5,  0.5)
        glVertex3f(-0.5, -0.5,  0.5)
        
        # Right face
        glVertex3f( 0.5, -0.5, -0.5)
        glVertex3f( 0.5,  0.5, -0.5)
        glVertex3f( 0.5,  0.5,  0.5)
        glVertex3f( 0.5, -0.5,  0.5)
        
        # Left face
        glVertex3f(-0.5, -0.5, -0.5)
        glVertex3f(-0.5, -0.5,  0.5)
        glVertex3f(-0.5,  0.5,  0.5)
        glVertex3f(-0.5,  0.5, -0.5)
        
        glEnd()
        glPopMatrix()
        
    def handle_input(self, dt):
        """Handle input with error protection."""
        try:
            # Get events safely
            events = pygame.event.get()
        except:
            # If events fail, just continue
            events = []
            
        for event in events:
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
            elif event.type == pygame.MOUSEMOTION:
                dx, dy = event.rel
                self.camera_yaw += dx * self.mouse_sensitivity
                self.camera_pitch -= dy * self.mouse_sensitivity
                self.camera_pitch = max(-1.5, min(1.5, self.camera_pitch))
        
        # Handle movement
        keys = pygame.key.get_pressed()
        move_x = 0
        move_z = 0
        
        if keys[pygame.K_w]:
            move_x += math.sin(self.camera_yaw)
            move_z += math.cos(self.camera_yaw)
        if keys[pygame.K_s]:
            move_x -= math.sin(self.camera_yaw)
            move_z -= math.cos(self.camera_yaw)
        if keys[pygame.K_a]:
            move_x += math.cos(self.camera_yaw)
            move_z -= math.sin(self.camera_yaw)
        if keys[pygame.K_d]:
            move_x -= math.cos(self.camera_yaw)
            move_z += math.sin(self.camera_yaw)
        if keys[pygame.K_SPACE]:
            self.camera_y += self.move_speed * dt
        if keys[pygame.K_LSHIFT]:
            self.camera_y -= self.move_speed * dt
            
        self.camera_x += move_x * self.move_speed * dt
        self.camera_z += move_z * self.move_speed * dt
        
    def render(self):
        """Render the scene."""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        glLoadIdentity()
        
        # Apply camera rotation
        glRotatef(math.degrees(self.camera_pitch), 1, 0, 0)
        glRotatef(math.degrees(self.camera_yaw), 0, 1, 0)
        
        # Apply camera translation
        glTranslatef(-self.camera_x, -self.camera_y, -self.camera_z)
        
        # Draw world
        # Ground
        for x in range(-10, 11, 2):
            for z in range(-10, 11, 2):
                self.draw_cube(x, 0, z, (0.2, 0.8, 0.2))  # Green grass
                
        # Some structures
        for i in range(3):
            x = (i - 1) * 6
            for y in range(1, 4):
                self.draw_cube(x, y, 8, (0.7, 0.5, 0.3))  # Brown blocks
                
        # Trees
        for i in range(-2, 3):
            x = i * 8
            z = -8
            self.draw_cube(x, 1, z, (0.4, 0.2, 0.1))  # Trunk
            self.draw_cube(x, 2, z, (0.4, 0.2, 0.1))  # Trunk
            self.draw_cube(x, 3, z, (0.1, 0.6, 0.1))  # Leaves
        
        pygame.display.flip()
        
    def run(self):
        """Main game loop."""
        clock = pygame.time.Clock()
        
        while self.running:
            dt = clock.tick(60) / 1000.0  # Delta time in seconds
            
            self.handle_input(dt)
            self.render()
            
        pygame.quit()
        print("Game ended.")

def main():
    """Main function."""
    game = BasicMinecraft()
    
    try:
        game.initialize()
        game.run()
        return True
    except Exception as e:
        print(f"Error: {e}")
        pygame.quit()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
