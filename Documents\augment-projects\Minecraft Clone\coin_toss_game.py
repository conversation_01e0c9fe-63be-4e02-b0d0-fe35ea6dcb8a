


















































1import random
import time

def coin_toss():
    """Simulate a coin toss and return 'Heads' or 'Tails'"""
    return random.choice(['Heads', 'Tails'])

def get_user_guess():
    """Get the user's guess for the coin toss"""
    while True:
        guess = input("Guess the coin toss (Heads/Tails): ").strip().lower()
        if guess in ['heads', 'tails']:
            return guess.capitalize()
        else:
            print("Please enter 'Heads' or 'Tails'")

def play_coin_toss_game():
    """Main game function"""
    print("Welcome to the Coin Toss Game!")
    print("=" * 30)
    
    score = 0
    total_games = 0
    
    while True:
        # Get user's guess
        user_guess = get_user_guess()
        
        # Simulate coin toss with dramatic effect
        print("\nFlipping the coin...")
        time.sleep(1)
        print("The coin is spinning...")
        time.sleep(1)
        
        # Get the actual result
        result = coin_toss()
        print(f"The coin landed on: {result}")
        
        # Check if user won
        total_games += 1
        if user_guess == result:
            print("🎉 Congratulations! You guessed correctly!")
            score += 1
        else:
            print("😔 Sorry, better luck next time!")
        
        # Show current score
        print(f"\nScore: {score}/{total_games}")
        print(f"Win rate: {(score/total_games)*100:.1f}%")
        
        # Ask if they want to play again
        play_again = input("\nDo you want to play again? (yes/no): ").strip().lower()
        if play_again not in ['yes', 'y']:
            break
        print("\n" + "-" * 30)
    
    # Final results
    print("\n" + "=" * 30)
    print("Game Over!")
    print(f"Final Score: {score}/{total_games}")
    print(f"Final Win Rate: {(score/total_games)*100:.1f}%")
    
    if score == total_games:
        print("Perfect game! You're a coin toss master! 🏆")
    elif score >= total_games * 0.7:
        print("Great job! You have good intuition! 🌟")
    elif score >= total_games * 0.5:
        print("Not bad! You're getting the hang of it! 👍")
    else:
        print("Keep practicing! Luck will come your way! 🍀")

def play_multiple_rounds():
    """Play multiple rounds and track statistics"""
    print("Multiple Rounds Mode!")
    print("=" * 30)
    
    try:
        rounds = int(input("How many rounds would you like to play? "))
        if rounds <= 0:
            print("Please enter a positive number.")
            return
    except ValueError:
        print("Please enter a valid number.")
        return
    
    score = 0
    results = []
    
    for round_num in range(1, rounds + 1):
        print(f"\n--- Round {round_num}/{rounds} ---")
        
        # Get user's guess
        user_guess = get_user_guess()
        
        # Get the actual result
        result = coin_toss()
        print(f"Result: {result}")
        
        # Check if user won
        if user_guess == result:
            print("✅ Correct!")
            score += 1
            results.append("Win")
        else:
            print("❌ Wrong!")
            results.append("Loss")
        
        print(f"Current score: {score}/{round_num}")
    
    # Final statistics
    print("\n" + "=" * 40)
    print("FINAL RESULTS")
    print("=" * 40)
    print(f"Total Score: {score}/{rounds}")
    print(f"Win Rate: {(score/rounds)*100:.1f}%")
    
    # Show pattern of wins/losses
    print(f"Results: {' '.join(results)}")
    
    # Performance feedback
    if score == rounds:
        print("🏆 PERFECT! You're a coin toss legend!")
    elif score >= rounds * 0.8:
        print("🌟 Excellent! You have amazing intuition!")
    elif score >= rounds * 0.6:
        print("👍 Good job! Above average performance!")
    elif score >= rounds * 0.4:
        print("😊 Not bad! Keep practicing!")
    else:
        print("🍀 Better luck next time! The odds will turn in your favor!")

def main_menu():
    """Main menu for the coin toss game"""
    while True:
        print("\n" + "=" * 40)
        print("COIN TOSS GAME")
        print("=" * 40)
        print("1. Play Interactive Game")
        print("2. Play Multiple Rounds")
        print("3. Quick Single Toss")
        print("4. Exit")
        
        choice = input("\nSelect an option (1-4): ").strip()
        
        if choice == '1':
            play_coin_toss_game()
        elif choice == '2':
            play_multiple_rounds()
        elif choice == '3':
            # Quick single toss
            user_guess = get_user_guess()
            result = coin_toss()
            print(f"\nYour guess: {user_guess}")
            print(f"Result: {result}")
            if user_guess == result:
                print("🎉 You got it right!")
            else:
                print("😔 Better luck next time!")
        elif choice == '4':
            print("Thanks for playing! Goodbye! 👋")
            break
        else:
            print("Invalid choice. Please select 1-4.")

if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\nGame interrupted. Thanks for playing!")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("Please try running the game again.")
