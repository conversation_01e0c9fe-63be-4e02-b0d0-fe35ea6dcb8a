"""
Player controller and physics system for the Minecraft clone.

This module handles player movement, collision detection, and physics simulation.
"""

import numpy as np
import pygame
from pygame.locals import *
from typing import Op<PERSON>, Tu<PERSON>, List
import math

from config import (
    GRAVITY, PLAYER_HEIGHT, PLAYER_WIDTH, PLAYER_EYE_HEIGHT,
    JUMP_VELOCITY, WALK_SPEED, SPRINT_SPEED, FRICTION,
    AIR_RESISTANCE, BlockType, MOUSE_SENSITIVITY, HOTBAR_SLOTS,
    is_solid
)
from math_utils import Vec3, AABB, ray_cast_voxel
from chunk import ChunkManager


class Player:
    """Represents the player character with physics and controls."""
    
    def __init__(self, position: Vec3 = None):
        """
        Initialize player.
        
        Args:
            position: Starting position (default: origin)
        """
        # Position and rotation
        self.position = position or Vec3(0, 80, 0)
        self.rotation = Vec3(0, 0, 0)  # pitch, yaw, roll
        
        # Velocity and physics
        self.velocity = Vec3(0, 0, 0)
        self.acceleration = Vec3(0, 0, 0)
        self.on_ground = False
        self.in_water = False
        self.is_flying = False
        self.is_sprinting = False
        self.is_crouching = False
        
        # Player dimensions
        self.height = PLAYER_HEIGHT
        self.width = PLAYER_WIDTH
        self.eye_height = PLAYER_EYE_HEIGHT
        
        # Movement state
        self.move_forward = 0
        self.move_right = 0
        self.move_up = 0
        
        # Inventory
        self.hotbar = [BlockType.STONE, BlockType.DIRT, BlockType.GRASS,
                       BlockType.WOOD, BlockType.COBBLESTONE, BlockType.GLASS,
                       BlockType.SAND, BlockType.PLANKS, BlockType.LEAVES]
        self.selected_slot = 0
        
        # Interaction
        self.reach_distance = 5.0
        self.break_time = 0.0
        self.breaking_block = None
        
        # Camera
        self.camera_offset = Vec3(0, self.eye_height, 0)
    
    def get_camera_position(self) -> Vec3:
        """Get camera position (eye level)."""
        return self.position + self.camera_offset
    
    def get_aabb(self) -> AABB:
        """Get player's axis-aligned bounding box."""
        half_width = self.width / 2
        return AABB(
            Vec3(self.position.x - half_width, 
                 self.position.y,
                 self.position.z - half_width),
            Vec3(self.position.x + half_width,
                 self.position.y + self.height,
                 self.position.z + half_width)
        )
    
    def handle_input(self, keys: dict, mouse_rel: Tuple[int, int]):
        """
        Handle keyboard and mouse input.
        
        Args:
            keys: Dictionary of pressed keys
            mouse_rel: Relative mouse movement
        """
        # Mouse look
        if mouse_rel:
            self.rotation.y -= mouse_rel[0] * MOUSE_SENSITIVITY
            self.rotation.x -= mouse_rel[1] * MOUSE_SENSITIVITY
            
            # Clamp pitch
            self.rotation.x = max(-math.pi/2, min(math.pi/2, self.rotation.x))
            
            # Wrap yaw
            if self.rotation.y > math.pi:
                self.rotation.y -= 2 * math.pi
            elif self.rotation.y < -math.pi:
                self.rotation.y += 2 * math.pi
        
        # Movement input
        self.move_forward = 0
        self.move_right = 0
        self.move_up = 0
        
        if keys.get(K_w, False):
            self.move_forward += 1
        if keys.get(K_s, False):
            self.move_forward -= 1
        if keys.get(K_a, False):
            self.move_right -= 1
        if keys.get(K_d, False):
            self.move_right += 1
        
        # Special movement
        self.is_sprinting = keys.get(K_LSHIFT, False) and self.move_forward > 0
        self.is_crouching = keys.get(K_LCTRL, False)
        
        # Jump
        if keys.get(K_SPACE, False):
            if self.is_flying:
                self.move_up = 1
            elif self.on_ground:
                self.velocity.y = JUMP_VELOCITY
        
        # Flying controls
        if self.is_flying:
            if keys.get(K_LSHIFT, False):
                self.move_up = -1
            elif keys.get(K_SPACE, False):
                self.move_up = 1
        
        # Hotbar selection
        for i in range(min(9, HOTBAR_SLOTS)):
            if keys.get(K_1 + i, False):
                self.selected_slot = i
    
    def handle_mouse_scroll(self, scroll: int):
        """Handle mouse scroll for hotbar selection."""
        self.selected_slot = (self.selected_slot - scroll) % HOTBAR_SLOTS
    
    def update(self, dt: float, chunk_manager: ChunkManager):
        """
        Update player physics and position.
        
        Args:
            dt: Delta time in seconds
            chunk_manager: Chunk manager for collision detection
        """
        # Calculate movement direction
        move_speed = SPRINT_SPEED if self.is_sprinting else WALK_SPEED
        if self.is_crouching:
            move_speed *= 0.3
        
        # Convert input to world space movement
        yaw = self.rotation.y
        forward = Vec3(math.sin(yaw), 0, -math.cos(yaw))
        right = Vec3(math.cos(yaw), 0, math.sin(yaw))
        
        # Apply movement
        movement = Vec3(0, 0, 0)
        if self.move_forward:
            movement = movement + forward * self.move_forward
        if self.move_right:
            movement = movement + right * self.move_right
        
        # Normalize diagonal movement
        if movement.length() > 0:
            movement = movement.normalize() * move_speed
        
        # Apply to velocity
        if self.is_flying:
            self.velocity.x = movement.x
            self.velocity.z = movement.z
            self.velocity.y = self.move_up * move_speed
        else:
            # Ground movement with friction
            self.velocity.x = movement.x
            self.velocity.z = movement.z
            
            # Apply gravity
            self.velocity.y += GRAVITY * dt
            
            # Terminal velocity
            self.velocity.y = max(self.velocity.y, -50.0)
        
        # Update position with collision detection
        self._update_position_with_collision(dt, chunk_manager)
        
        # Apply friction
        if self.on_ground and not self.is_flying:
            self.velocity.x *= FRICTION
            self.velocity.z *= FRICTION
        else:
            self.velocity.x *= AIR_RESISTANCE
            self.velocity.z *= AIR_RESISTANCE
        
        # Check if in water
        eye_pos = self.get_camera_position()
        water_block = chunk_manager.get_block_world(
            int(eye_pos.x), int(eye_pos.y), int(eye_pos.z)
        )
        self.in_water = water_block == BlockType.WATER
        
        # Water physics
        if self.in_water:
            self.velocity = self.velocity * 0.8
            if self.velocity.y < -0.5:
                self.velocity.y = -0.5
    
    def _update_position_with_collision(self, dt: float, chunk_manager: ChunkManager):
        """Update position with collision detection."""
        # Get current AABB
        aabb = self.get_aabb()
        
        # Calculate desired position
        delta = self.velocity * dt
        
        # Check collision for each axis separately
        # This prevents getting stuck in corners
        
        # X-axis movement
        new_x = self.position.x + delta.x
        test_aabb = AABB(
            Vec3(new_x - self.width/2, aabb.min.y, aabb.min.z),
            Vec3(new_x + self.width/2, aabb.max.y, aabb.max.z)
        )
        if not self._check_collision(test_aabb, chunk_manager):
            self.position.x = new_x
        else:
            self.velocity.x = 0
        
        # Y-axis movement
        new_y = self.position.y + delta.y
        test_aabb = AABB(
            Vec3(self.position.x - self.width/2, new_y, aabb.min.z),
            Vec3(self.position.x + self.width/2, new_y + self.height, aabb.max.z)
        )
        was_on_ground = self.on_ground
        self.on_ground = False
        
        if not self._check_collision(test_aabb, chunk_manager):
            self.position.y = new_y
        else:
            if delta.y < 0:  # Falling
                self.on_ground = True
            self.velocity.y = 0
        
        # Step up small blocks automatically
        if was_on_ground and not self.on_ground and delta.y >= 0:
            # Try stepping up
            step_height = 0.6
            step_aabb = AABB(
                Vec3(self.position.x - self.width/2, 
                     self.position.y + step_height, 
                     aabb.min.z),
                Vec3(self.position.x + self.width/2,
                     self.position.y + self.height + step_height,
                     aabb.max.z)
            )
            if not self._check_collision(step_aabb, chunk_manager):
                self.position.y += step_height
                self.on_ground = True
        
        # Z-axis movement
        new_z = self.position.z + delta.z
        test_aabb = AABB(
            Vec3(self.position.x - self.width/2, self.position.y, new_z - self.width/2),
            Vec3(self.position.x + self.width/2, self.position.y + self.height, new_z + self.width/2)
        )
        if not self._check_collision(test_aabb, chunk_manager):
            self.position.z = new_z
        else:
            self.velocity.z = 0
    
    def _check_collision(self, aabb: AABB, chunk_manager: ChunkManager) -> bool:
        """Check if AABB collides with any solid blocks."""
        # Get block range to check
        min_x = int(math.floor(aabb.min.x))
        max_x = int(math.floor(aabb.max.x))
        min_y = int(math.floor(aabb.min.y))
        max_y = int(math.floor(aabb.max.y))
        min_z = int(math.floor(aabb.min.z))
        max_z = int(math.floor(aabb.max.z))
        
        # Check each block
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                for z in range(min_z, max_z + 1):
                    block_type = chunk_manager.get_block_world(x, y, z)
                    
                    if is_solid(block_type):
                        # Create block AABB
                        block_aabb = AABB(
                            Vec3(x, y, z),
                            Vec3(x + 1, y + 1, z + 1)
                        )
                        
                        # Check intersection
                        if aabb.intersects(block_aabb):
                            return True
        
        return False
    
    def get_looking_at(self, chunk_manager: ChunkManager, 
                      max_distance: float = None) -> Optional[Tuple[Tuple[int, int, int], 
                                                                    Tuple[int, int, int]]]:
        """
        Get the block the player is looking at.
        
        Args:
            chunk_manager: Chunk manager for block queries
            max_distance: Maximum ray distance (default: reach_distance)
            
        Returns:
            Tuple of (block_pos, previous_pos) or None if no block hit
        """
        max_distance = max_distance or self.reach_distance
        
        # Get ray origin and direction
        eye_pos = self.get_camera_position()
        origin = np.array([eye_pos.x, eye_pos.y, eye_pos.z])
        
        # Calculate ray direction from rotation
        pitch = self.rotation.x
        yaw = self.rotation.y
        
        direction = np.array([
            math.sin(yaw) * math.cos(pitch),
            math.sin(pitch),
            -math.cos(yaw) * math.cos(pitch)
        ])
        
        # Cast ray
        voxels = ray_cast_voxel(origin, direction, max_distance)
        
        # Check each voxel
        previous_pos = None
        for voxel_pos in voxels:
            x, y, z = voxel_pos
            block_type = chunk_manager.get_block_world(x, y, z)
            
            if block_type != BlockType.AIR:
                return (voxel_pos, previous_pos)
            
            previous_pos = voxel_pos
        
        return None
    
    def break_block(self, chunk_manager: ChunkManager) -> bool:
        """
        Break the block the player is looking at.
        
        Args:
            chunk_manager: Chunk manager for block operations
            
        Returns:
            True if a block was broken
        """
        result = self.get_looking_at(chunk_manager)
        if result:
            block_pos, _ = result
            x, y, z = block_pos
            
            # Check if bedrock (unbreakable)
            if chunk_manager.get_block_world(x, y, z) == BlockType.BEDROCK:
                return False
            
            # Break block
            chunk_manager.set_block_world(x, y, z, BlockType.AIR)
            chunk_manager.mark_dirty_around(x, y, z)
            return True
        
        return False
    
    def place_block(self, chunk_manager: ChunkManager) -> bool:
        """
        Place a block from the hotbar.
        
        Args:
            chunk_manager: Chunk manager for block operations
            
        Returns:
            True if a block was placed
        """
        # Get selected block type
        if self.selected_slot >= len(self.hotbar):
            return False
        
        block_type = self.hotbar[self.selected_slot]
        if block_type == BlockType.AIR:
            return False
        
        # Get block we're looking at
        result = self.get_looking_at(chunk_manager)
        if not result:
            return False
        
        block_pos, previous_pos = result
        if not previous_pos:
            return False
        
        # Use the face position for placement
        place_x, place_y, place_z = previous_pos
        
        # Check if placement position is valid (not inside player)
        place_aabb = AABB(
            Vec3(place_x, place_y, place_z),
            Vec3(place_x + 1, place_y + 1, place_z + 1)
        )
        
        if self.get_aabb().intersects(place_aabb):
            return False
        
        # Check if position is air
        if chunk_manager.get_block_world(place_x, place_y, place_z) != BlockType.AIR:
            return False
        
        # Place block
        chunk_manager.set_block_world(place_x, place_y, place_z, block_type)
        chunk_manager.mark_dirty_around(place_x, place_y, place_z)
        return True
    
    def toggle_flying(self):
        """Toggle flying mode."""
        self.is_flying = not self.is_flying
        if self.is_flying:
            self.velocity.y = 0
    
    def respawn(self, spawn_position: Vec3):
        """Respawn player at given position."""
        self.position = spawn_position
        self.velocity = Vec3(0, 0, 0)
        self.rotation = Vec3(0, 0, 0)
        self.on_ground = False
        self.is_flying = False
        self.breaking_block = None
        self.break_time = 0.0