"""
Mathematical utilities for the <PERSON><PERSON> clone.

This module provides vector operations, matrix transformations,
collision detection, and other mathematical functions.
"""

import numpy as np
from typing import <PERSON><PERSON>, List, Optional
from numba import jit
import math


class Vec3:
    """3D vector class with common operations."""
    
    def __init__(self, x: float = 0, y: float = 0, z: float = 0):
        """Initialize a 3D vector."""
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    @classmethod
    def from_array(cls, arr: np.ndarray) -> 'Vec3':
        """Create a Vec3 from a numpy array."""
        return cls(arr[0], arr[1], arr[2])
    
    def to_array(self) -> np.ndarray:
        """Convert to numpy array."""
        return np.array([self.x, self.y, self.z], dtype=np.float32)
    
    def __add__(self, other: 'Vec3') -> 'Vec3':
        """Add two vectors."""
        return Vec3(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other: 'Vec3') -> 'Vec3':
        """Subtract two vectors."""
        return Vec3(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar: float) -> 'Vec3':
        """Multiply vector by scalar."""
        return Vec3(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __truediv__(self, scalar: float) -> 'Vec3':
        """Divide vector by scalar."""
        return Vec3(self.x / scalar, self.y / scalar, self.z / scalar)
    
    def dot(self, other: 'Vec3') -> float:
        """Dot product of two vectors."""
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other: 'Vec3') -> 'Vec3':
        """Cross product of two vectors."""
        return Vec3(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x
        )
    
    def length(self) -> float:
        """Length of the vector."""
        return math.sqrt(self.x ** 2 + self.y ** 2 + self.z ** 2)
    
    def length_squared(self) -> float:
        """Squared length of the vector (avoids sqrt)."""
        return self.x ** 2 + self.y ** 2 + self.z ** 2
    
    def normalize(self) -> 'Vec3':
        """Return normalized vector."""
        length = self.length()
        if length > 0:
            return self / length
        return Vec3(0, 0, 0)
    
    def __repr__(self) -> str:
        """String representation."""
        return f"Vec3({self.x:.2f}, {self.y:.2f}, {self.z:.2f})"


class AABB:
    """Axis-Aligned Bounding Box for collision detection."""
    
    def __init__(self, min_point: Vec3, max_point: Vec3):
        """Initialize AABB with min and max points."""
        self.min = min_point
        self.max = max_point
    
    @classmethod
    def from_position_size(cls, position: Vec3, size: Vec3) -> 'AABB':
        """Create AABB from position and size."""
        half_size = size * 0.5
        return cls(position - half_size, position + half_size)
    
    def intersects(self, other: 'AABB') -> bool:
        """Check if this AABB intersects with another."""
        return (self.min.x <= other.max.x and self.max.x >= other.min.x and
                self.min.y <= other.max.y and self.max.y >= other.min.y and
                self.min.z <= other.max.z and self.max.z >= other.min.z)
    
    def contains_point(self, point: Vec3) -> bool:
        """Check if a point is inside this AABB."""
        return (self.min.x <= point.x <= self.max.x and
                self.min.y <= point.y <= self.max.y and
                self.min.z <= point.z <= self.max.z)
    
    def expand(self, amount: float) -> 'AABB':
        """Return an expanded AABB."""
        expansion = Vec3(amount, amount, amount)
        return AABB(self.min - expansion, self.max + expansion)
    
    def translate(self, offset: Vec3) -> 'AABB':
        """Return a translated AABB."""
        return AABB(self.min + offset, self.max + offset)


class Frustum:
    """View frustum for culling."""
    
    def __init__(self):
        """Initialize frustum with 6 planes."""
        self.planes = np.zeros((6, 4), dtype=np.float32)  # ax + by + cz + d = 0
    
    def update_from_matrix(self, mvp_matrix: np.ndarray):
        """
        Update frustum planes from model-view-projection matrix.
        
        Args:
            mvp_matrix: 4x4 MVP matrix
        """
        # Extract planes from MVP matrix
        # Left plane
        self.planes[0] = mvp_matrix[3] + mvp_matrix[0]
        # Right plane
        self.planes[1] = mvp_matrix[3] - mvp_matrix[0]
        # Bottom plane
        self.planes[2] = mvp_matrix[3] + mvp_matrix[1]
        # Top plane
        self.planes[3] = mvp_matrix[3] - mvp_matrix[1]
        # Near plane
        self.planes[4] = mvp_matrix[3] + mvp_matrix[2]
        # Far plane
        self.planes[5] = mvp_matrix[3] - mvp_matrix[2]
        
        # Normalize planes
        for i in range(6):
            length = np.linalg.norm(self.planes[i][:3])
            if length > 0:
                self.planes[i] /= length
    
    def contains_aabb(self, aabb: AABB) -> bool:
        """
        Check if an AABB is inside or intersects the frustum.
        
        Args:
            aabb: Axis-aligned bounding box to test
            
        Returns:
            True if AABB is at least partially inside frustum
        """
        for plane in self.planes:
            # Find the vertex furthest in the direction of the plane normal
            vertex = Vec3(
                aabb.max.x if plane[0] > 0 else aabb.min.x,
                aabb.max.y if plane[1] > 0 else aabb.min.y,
                aabb.max.z if plane[2] > 0 else aabb.min.z
            )
            
            # If this vertex is outside the plane, the whole AABB is outside
            if (plane[0] * vertex.x + plane[1] * vertex.y + 
                plane[2] * vertex.z + plane[3] < 0):
                return False
        
        return True


@jit(nopython=True)
def ray_cast_voxel(origin: np.ndarray, direction: np.ndarray, 
                   max_distance: float) -> List[Tuple[int, int, int]]:
    """
    Cast a ray through voxel space using DDA algorithm.
    
    Args:
        origin: Ray origin [x, y, z]
        direction: Ray direction (normalized) [dx, dy, dz]
        max_distance: Maximum ray distance
        
    Returns:
        List of voxel coordinates the ray passes through
    """
    # Current voxel position
    x = int(np.floor(origin[0]))
    y = int(np.floor(origin[1]))
    z = int(np.floor(origin[2]))
    
    # Calculate step direction
    step_x = 1 if direction[0] > 0 else -1
    step_y = 1 if direction[1] > 0 else -1
    step_z = 1 if direction[2] > 0 else -1
    
    # Calculate t values for next voxel boundary
    if direction[0] != 0:
        t_max_x = ((x + (1 if step_x > 0 else 0)) - origin[0]) / direction[0]
        t_delta_x = step_x / direction[0]
    else:
        t_max_x = float('inf')
        t_delta_x = float('inf')
    
    if direction[1] != 0:
        t_max_y = ((y + (1 if step_y > 0 else 0)) - origin[1]) / direction[1]
        t_delta_y = step_y / direction[1]
    else:
        t_max_y = float('inf')
        t_delta_y = float('inf')
    
    if direction[2] != 0:
        t_max_z = ((z + (1 if step_z > 0 else 0)) - origin[2]) / direction[2]
        t_delta_z = step_z / direction[2]
    else:
        t_max_z = float('inf')
        t_delta_z = float('inf')
    
    # Traverse voxels
    voxels = []
    distance = 0.0
    
    while distance < max_distance:
        voxels.append((x, y, z))
        
        # Find next voxel boundary
        if t_max_x < t_max_y and t_max_x < t_max_z:
            x += step_x
            distance = t_max_x
            t_max_x += t_delta_x
        elif t_max_y < t_max_z:
            y += step_y
            distance = t_max_y
            t_max_y += t_delta_y
        else:
            z += step_z
            distance = t_max_z
            t_max_z += t_delta_z
    
    return voxels


def create_perspective_matrix(fov: float, aspect: float, 
                            near: float, far: float) -> np.ndarray:
    """
    Create a perspective projection matrix.
    
    Args:
        fov: Field of view in degrees
        aspect: Aspect ratio (width/height)
        near: Near clipping plane
        far: Far clipping plane
        
    Returns:
        4x4 perspective projection matrix
    """
    fov_rad = math.radians(fov)
    f = 1.0 / math.tan(fov_rad / 2.0)
    
    matrix = np.zeros((4, 4), dtype=np.float32)
    matrix[0, 0] = f / aspect
    matrix[1, 1] = f
    matrix[2, 2] = (far + near) / (near - far)
    matrix[2, 3] = (2 * far * near) / (near - far)
    matrix[3, 2] = -1
    
    return matrix


def create_view_matrix(eye: Vec3, target: Vec3, up: Vec3) -> np.ndarray:
    """
    Create a view matrix (look-at matrix).
    
    Args:
        eye: Camera position
        target: Target position to look at
        up: Up vector
        
    Returns:
        4x4 view matrix
    """
    # Calculate basis vectors
    forward = (target - eye).normalize()
    right = forward.cross(up).normalize()
    up_vec = right.cross(forward)
    
    # Create rotation matrix
    matrix = np.identity(4, dtype=np.float32)
    matrix[0, :3] = right.to_array()
    matrix[1, :3] = up_vec.to_array()
    matrix[2, :3] = -forward.to_array()
    
    # Apply translation
    matrix[0, 3] = -right.dot(eye)
    matrix[1, 3] = -up_vec.dot(eye)
    matrix[2, 3] = forward.dot(eye)
    
    return matrix


def create_rotation_matrix(pitch: float, yaw: float, roll: float = 0) -> np.ndarray:
    """
    Create a rotation matrix from Euler angles.
    
    Args:
        pitch: Rotation around X axis (radians)
        yaw: Rotation around Y axis (radians)
        roll: Rotation around Z axis (radians)
        
    Returns:
        4x4 rotation matrix
    """
    cos_p = math.cos(pitch)
    sin_p = math.sin(pitch)
    cos_y = math.cos(yaw)
    sin_y = math.sin(yaw)
    cos_r = math.cos(roll)
    sin_r = math.sin(roll)
    
    matrix = np.identity(4, dtype=np.float32)
    
    # Combined rotation matrix (Y * X * Z order)
    matrix[0, 0] = cos_y * cos_r + sin_y * sin_p * sin_r
    matrix[0, 1] = cos_p * sin_r
    matrix[0, 2] = sin_y * cos_r - cos_y * sin_p * sin_r
    
    matrix[1, 0] = -cos_y * sin_r + sin_y * sin_p * cos_r
    matrix[1, 1] = cos_p * cos_r
    matrix[1, 2] = -sin_y * sin_r - cos_y * sin_p * cos_r
    
    matrix[2, 0] = -sin_y * cos_p
    matrix[2, 1] = sin_p
    matrix[2, 2] = cos_y * cos_p
    
    return matrix


def world_to_chunk_coords(world_x: float, world_y: float, world_z: float, 
                         chunk_size: int) -> Tuple[int, int, int]:
    """
    Convert world coordinates to chunk coordinates.
    
    Args:
        world_x, world_y, world_z: World position
        chunk_size: Size of chunks
        
    Returns:
        Tuple of (chunk_x, chunk_y, chunk_z)
    """
    chunk_x = int(math.floor(world_x / chunk_size))
    chunk_y = int(math.floor(world_y / chunk_size))
    chunk_z = int(math.floor(world_z / chunk_size))
    return chunk_x, chunk_y, chunk_z


def chunk_to_world_coords(chunk_x: int, chunk_y: int, chunk_z: int,
                         chunk_size: int) -> Tuple[int, int, int]:
    """
    Convert chunk coordinates to world coordinates (corner of chunk).
    
    Args:
        chunk_x, chunk_y, chunk_z: Chunk position
        chunk_size: Size of chunks
        
    Returns:
        Tuple of (world_x, world_y, world_z)
    """
    return chunk_x * chunk_size, chunk_y * chunk_size, chunk_z * chunk_size


def lerp(a: float, b: float, t: float) -> float:
    """Linear interpolation between two values."""
    return a + (b - a) * t


def clamp(value: float, min_val: float, max_val: float) -> float:
    """Clamp a value between min and max."""
    return max(min_val, min(value, max_val))


def smoothstep(edge0: float, edge1: float, x: float) -> float:
    """Smooth interpolation function."""
    t = clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0)
    return t * t * (3.0 - 2.0 * t)
