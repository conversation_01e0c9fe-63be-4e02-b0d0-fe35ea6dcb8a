"""
World management system for the Minecraft clone.

This module handles chunk loading/unloading, terrain generation coordination,
and world state management.
"""

import threading
import queue
from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor
from typing import Dict, List, Tuple, Optional, Set
import time
import os
import pickle
import numpy as np

from minecraft_config import (
    CHUNK_SIZE, RENDER_DISTANCE, CHUNK_LOAD_PER_FRAME,
    CHUNK_UNLOAD_DISTANCE, MULTIPROCESSING_ENABLED,
    WORKER_PROCESSES, MAX_CHUNKS_LOADED, SAVE_DIRECTORY,
    WORLD_WIDTH, WORLD_HEIGHT, WORLD_DEPTH, BlockType
)
from minecraft_math_utils import Vec3, world_to_chunk_coords
from minecraft_chunk import Chunk, ChunkManager, ChunkState
from minecraft_noise import TerrainGenerator
from minecraft_player import Player


class WorldManager:
    """Manages the game world including chunk generation and loading."""
    
    def __init__(self, seed: int = None):
        """
        Initialize world manager.
        
        Args:
            seed: World seed for terrain generation
        """
        self.seed = seed or int(time.time())
        self.chunk_manager = ChunkManager(MAX_CHUNKS_LOADED)
        self.terrain_generator = TerrainGenerator(self.seed)
        
        # Chunk generation queues
        self.generation_queue = queue.PriorityQueue()
        self.generation_results = queue.Queue()
        
        # Track loaded chunks
        self.loaded_chunks: Set[Tuple[int, int, int]] = set()
        self.generating_chunks: Set[Tuple[int, int, int]] = set()
        
        # Threading
        self.generation_thread = None
        self.executor = None
        self.running = False
        
        # World bounds
        self.world_bounds = {
            'min_x': 0,
            'max_x': WORLD_WIDTH,
            'min_y': 0,
            'max_y': WORLD_HEIGHT,
            'min_z': 0,
            'max_z': WORLD_DEPTH
        }
        
        # Statistics
        self.chunks_generated = 0
        self.generation_time_total = 0.0
        
        # Save data
        self.world_name = f"world_{self.seed}"
        self.save_path = os.path.join(SAVE_DIRECTORY, self.world_name)
    
    def start(self):
        """Start world generation threads."""
        self.running = True
        
        # Create executor
        if MULTIPROCESSING_ENABLED:
            self.executor = ProcessPoolExecutor(max_workers=WORKER_PROCESSES)
        else:
            self.executor = ThreadPoolExecutor(max_workers=WORKER_PROCESSES)
        
        # Start generation thread
        self.generation_thread = threading.Thread(target=self._generation_worker)
        self.generation_thread.daemon = True
        self.generation_thread.start()
    
    def stop(self):
        """Stop world generation threads."""
        self.running = False
        
        if self.generation_thread:
            self.generation_thread.join(timeout=5.0)
        
        if self.executor:
            self.executor.shutdown(wait=True)
    
    def update(self, player_position: Vec3, dt: float):
        """
        Update world state based on player position.
        
        Args:
            player_position: Current player position
            dt: Delta time
        """
        # Get player chunk coordinates
        player_chunk = world_to_chunk_coords(
            player_position.x, player_position.y, player_position.z,
            CHUNK_SIZE
        )
        
        # Queue chunks for loading
        self._queue_nearby_chunks(player_chunk)
        
        # Process generation results
        self._process_generation_results()
        
        # Unload distant chunks
        self._unload_distant_chunks(player_chunk)
    
    def _queue_nearby_chunks(self, center: Tuple[int, int, int]):
        """Queue nearby chunks for generation."""
        cx, cy, cz = center
        
        # Check chunks in render distance
        for dx in range(-RENDER_DISTANCE, RENDER_DISTANCE + 1):
            for dy in range(-2, 3):  # Limited vertical range
                for dz in range(-RENDER_DISTANCE, RENDER_DISTANCE + 1):
                    chunk_x = cx + dx
                    chunk_y = cy + dy
                    chunk_z = cz + dz
                    
                    # Check bounds
                    if not self._is_chunk_in_bounds(chunk_x, chunk_y, chunk_z):
                        continue
                    
                    # Check distance
                    dist_sq = dx * dx + dy * dy + dz * dz
                    if dist_sq > RENDER_DISTANCE * RENDER_DISTANCE:
                        continue
                    
                    chunk_pos = (chunk_x, chunk_y, chunk_z)
                    
                    # Skip if already loaded or generating
                    if (chunk_pos in self.loaded_chunks or
                        chunk_pos in self.generating_chunks):
                        continue
                    
                    # Add to generation queue with priority
                    priority = dist_sq  # Closer chunks have higher priority
                    self.generation_queue.put((priority, chunk_pos))
                    self.generating_chunks.add(chunk_pos)
    
    def _generation_worker(self):
        """Worker thread for chunk generation."""
        while self.running:
            try:
                # Get chunk from queue
                priority, chunk_pos = self.generation_queue.get(timeout=0.1)
            except queue.Empty:
                continue
            
            # Generate chunk
            chunk_x, chunk_y, chunk_z = chunk_pos
            
            try:
                # Submit generation task
                future = self.executor.submit(
                    self._generate_chunk_data,
                    chunk_x, chunk_y, chunk_z,
                    self.seed
                )
                
                # Store result when ready
                result = future.result(timeout=5.0)
                self.generation_results.put((chunk_pos, result))
                
            except Exception as e:
                print(f"Error generating chunk {chunk_pos}: {e}")
                self.generating_chunks.discard(chunk_pos)
    
    @staticmethod
    def _generate_chunk_data(chunk_x: int, chunk_y: int, chunk_z: int,
                           seed: int) -> np.ndarray:
        """
        Generate chunk data (static method for multiprocessing).
        
        Args:
            chunk_x, chunk_y, chunk_z: Chunk coordinates
            seed: World seed
            
        Returns:
            3D numpy array of block types
        """
        # Create generator with seed
        generator = TerrainGenerator(seed)
        
        # Generate blocks
        blocks = generator.generate_chunk_blocks(
            chunk_x, chunk_y, chunk_z, CHUNK_SIZE
        )
        
        return blocks
    
    def _process_generation_results(self):
        """Process completed chunk generation results."""
        processed = 0
        
        while processed < CHUNK_LOAD_PER_FRAME:
            try:
                chunk_pos, block_data = self.generation_results.get_nowait()
            except queue.Empty:
                break
            
            chunk_x, chunk_y, chunk_z = chunk_pos
            
            # Create chunk
            chunk = Chunk(chunk_x, chunk_y, chunk_z)
            chunk.set_blocks(block_data)
            
            # Add structures
            self._add_chunk_structures(chunk)
            
            # Add to manager
            self.chunk_manager.add_chunk(chunk)
            
            # Update tracking
            self.loaded_chunks.add(chunk_pos)
            self.generating_chunks.discard(chunk_pos)
            self.chunks_generated += 1
            
            # Mark as ready for meshing
            chunk.state = ChunkState.GENERATED
            
            processed += 1
    
    def _add_chunk_structures(self, chunk: Chunk):
        """Add structures like trees to a chunk after generation."""
        # Get biomes for chunk positions
        biomes = {}
        for x in range(CHUNK_SIZE):
            for z in range(CHUNK_SIZE):
                world_x = chunk.world_x + x
                world_z = chunk.world_z + z
                biomes[(x, z)] = self.terrain_generator.get_biome(world_x, world_z)
        
        # Generate structures
        self.terrain_generator.generate_structures(
            chunk.chunk_x, chunk.chunk_z,
            chunk.blocks, biomes
        )
    
    def _unload_distant_chunks(self, center: Tuple[int, int, int]):
        """Unload chunks that are too far from center."""
        cx, cy, cz = center
        chunks_to_unload = []
        
        for chunk_pos in self.loaded_chunks:
            chunk_x, chunk_y, chunk_z = chunk_pos
            
            # Calculate distance
            dx = chunk_x - cx
            dy = chunk_y - cy
            dz = chunk_z - cz
            dist_sq = dx * dx + dy * dy + dz * dz
            
            # Check if should unload
            if dist_sq > CHUNK_UNLOAD_DISTANCE * CHUNK_UNLOAD_DISTANCE:
                chunks_to_unload.append(chunk_pos)
        
        # Unload chunks
        for chunk_pos in chunks_to_unload:
            self._unload_chunk(chunk_pos)
    
    def _unload_chunk(self, chunk_pos: Tuple[int, int, int]):
        """Unload a single chunk."""
        chunk_x, chunk_y, chunk_z = chunk_pos
        
        # Get chunk
        chunk = self.chunk_manager.get_chunk(chunk_x, chunk_y, chunk_z)
        if not chunk:
            return
        
        # Save if modified
        if chunk.is_dirty:
            self._save_chunk(chunk)
        
        # Remove from manager
        self.chunk_manager.remove_chunk(chunk_x, chunk_y, chunk_z)
        self.loaded_chunks.discard(chunk_pos)
    
    def _is_chunk_in_bounds(self, chunk_x: int, chunk_y: int, 
                          chunk_z: int) -> bool:
        """Check if chunk coordinates are within world bounds."""
        world_x = chunk_x * CHUNK_SIZE
        world_y = chunk_y * CHUNK_SIZE
        world_z = chunk_z * CHUNK_SIZE
        
        return (self.world_bounds['min_x'] <= world_x < self.world_bounds['max_x'] and
                self.world_bounds['min_y'] <= world_y < self.world_bounds['max_y'] and
                self.world_bounds['min_z'] <= world_z < self.world_bounds['max_z'])
    
    def get_spawn_position(self) -> Vec3:
        """Get world spawn position."""
        # Find spawn position in center of world
        spawn_x = WORLD_WIDTH // 2
        spawn_z = WORLD_DEPTH // 2
        
        # Load spawn chunk
        spawn_chunk_x = spawn_x // CHUNK_SIZE
        spawn_chunk_z = spawn_z // CHUNK_SIZE
        
        # Generate spawn area chunks
        for dx in range(-1, 2):
            for dz in range(-1, 2):
                chunk_pos = (spawn_chunk_x + dx, 4, spawn_chunk_z + dz)
                if chunk_pos not in self.loaded_chunks:
                    # Generate synchronously for spawn
                    blocks = self._generate_chunk_data(
                        chunk_pos[0], chunk_pos[1], chunk_pos[2],
                        self.seed
                    )
                    chunk = Chunk(*chunk_pos)
                    chunk.set_blocks(blocks)
                    self.chunk_manager.add_chunk(chunk)
                    self.loaded_chunks.add(chunk_pos)
        
        # Find highest solid block
        for y in range(WORLD_HEIGHT - 1, -1, -1):
            block_type = self.chunk_manager.get_block_world(spawn_x, y, spawn_z)
            if block_type != BlockType.AIR:
                return Vec3(spawn_x + 0.5, y + 1.5, spawn_z + 0.5)
        
        # Default spawn position
        return Vec3(spawn_x + 0.5, 80, spawn_z + 0.5)
    
    def save_world(self):
        """Save the entire world to disk."""
        # Create save directory
        os.makedirs(self.save_path, exist_ok=True)
        
        # Save world metadata
        metadata = {
            'seed': self.seed,
            'chunks_generated': self.chunks_generated,
            'world_bounds': self.world_bounds,
            'version': 1
        }
        
        with open(os.path.join(self.save_path, 'world.meta'), 'wb') as f:
            pickle.dump(metadata, f)
        
        # Save all loaded chunks
        for chunk_pos in self.loaded_chunks:
            chunk_x, chunk_y, chunk_z = chunk_pos
            chunk = self.chunk_manager.get_chunk(chunk_x, chunk_y, chunk_z)
            if chunk:
                self._save_chunk(chunk)
    
    def _save_chunk(self, chunk: Chunk):
        """Save a single chunk to disk."""
        # Ensure save directory exists
        os.makedirs(self.save_path, exist_ok=True)

        chunk_filename = f"chunk_{chunk.chunk_x}_{chunk.chunk_y}_{chunk.chunk_z}.dat"
        chunk_path = os.path.join(self.save_path, chunk_filename)

        # Compress and save
        compressed_data = chunk.compress()
        with open(chunk_path, 'wb') as f:
            f.write(compressed_data)
    
    def load_world(self, world_name: str) -> bool:
        """
        Load world from disk.
        
        Args:
            world_name: Name of world to load
            
        Returns:
            True if successful
        """
        load_path = os.path.join(SAVE_DIRECTORY, world_name)
        
        if not os.path.exists(load_path):
            return False
        
        try:
            # Load metadata
            with open(os.path.join(load_path, 'world.meta'), 'rb') as f:
                metadata = pickle.load(f)
            
            self.seed = metadata['seed']
            self.chunks_generated = metadata['chunks_generated']
            self.world_bounds = metadata['world_bounds']
            self.world_name = world_name
            self.save_path = load_path
            
            # Recreate terrain generator with loaded seed
            self.terrain_generator = TerrainGenerator(self.seed)
            
            return True
            
        except Exception as e:
            print(f"Error loading world: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, any]:
        """Get world statistics."""
        return {
            'seed': self.seed,
            'chunks_loaded': len(self.loaded_chunks),
            'chunks_generated': self.chunks_generated,
            'chunks_generating': len(self.generating_chunks),
            'generation_queue_size': self.generation_queue.qsize(),
            **self.chunk_manager.get_statistics()
        }