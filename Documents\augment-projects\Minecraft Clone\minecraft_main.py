"""
Main game loop and application entry point for the Minecraft clone.

This module coordinates all game systems and manages the main game loop.
"""

import pygame
import sys
import time
from typing import Optional
from enum import Enum, auto

from config import (
    TARGET_FPS, WINDOW_WIDTH, WINDOW_HEIGHT, SHOW_FPS,
    SHOW_COORDINATES, DEBUG_MODE, BlockType
)
from math_utils import Vec3
from renderer import Renderer
from world import WorldManager
from player import Player
from input_handler import InputHandler, InputAction
from chunk import ChunkState


class GameState(Enum):
    """Game state enumeration."""
    LOADING = auto()
    PLAYING = auto()
    PAUSED = auto()
    INVENTORY = auto()
    MENU = auto()


class MinecraftGame:
    """Main game class that manages all systems."""
    
    def __init__(self):
        """Initialize the game."""
        # Core systems
        self.renderer = None
        self.world_manager = None
        self.player = None
        self.input_handler = None
        
        # Game state
        self.state = GameState.LOADING
        self.running = False
        self.clock = pygame.time.Clock()
        
        # Time tracking
        self.current_time = 0.0
        self.day_time = 0.25  # Start at sunrise
        self.day_length = 600.0  # 10 minutes per day
        
        # Debug info
        self.show_debug = DEBUG_MODE
        self.show_wireframe = False
        self.fps_history = []
        
        # Performance stats
        self.frame_count = 0
        self.last_fps_update = 0.0
        self.current_fps = 0.0
    
    def initialize(self, world_seed: Optional[int] = None):
        """
        Initialize all game systems.
        
        Args:
            world_seed: Optional world seed
        """
        print("Initializing Minecraft Clone...")
        
        # Initialize renderer
        print("- Initializing renderer...")
        self.renderer = Renderer()
        self.renderer.initialize()
        
        # Initialize world
        print("- Creating world...")
        self.world_manager = WorldManager(world_seed)
        self.world_manager.start()
        
        # Get spawn position
        spawn_pos = self.world_manager.get_spawn_position()
        
        # Initialize player
        print("- Creating player...")
        self.player = Player(spawn_pos)
        
        # Initialize input
        print("- Setting up input...")
        self.input_handler = InputHandler()
        self.input_handler.capture_mouse(True)
        
        # Register input callbacks
        self._register_input_callbacks()
        
        # Set initial state
        self.state = GameState.PLAYING
        self.running = True
        
        print("Initialization complete!")
    
    def _register_input_callbacks(self):
        """Register callbacks for input actions."""
        # Toggle actions
        self.input_handler.register_callback(
            InputAction.TOGGLE_DEBUG, lambda e: self._toggle_debug()
        )
        self.input_handler.register_callback(
            InputAction.TOGGLE_WIREFRAME, lambda e: self._toggle_wireframe()
        )
        self.input_handler.register_callback(
            InputAction.TOGGLE_FLYING, lambda e: self.player.toggle_flying()
        )
        self.input_handler.register_callback(
            InputAction.PAUSE, lambda e: self._toggle_pause()
        )
        self.input_handler.register_callback(
            InputAction.QUIT, lambda e: self.quit()
        )
    
    def run(self):
        """Run the main game loop."""
        print("\nStarting game loop...")
        last_time = time.time()
        
        while self.running:
            # Calculate delta time
            current_time = time.time()
            dt = current_time - last_time
            last_time = current_time
            self.current_time = current_time
            
            # Process input
            self._process_input()
            
            # Update game state
            if self.state == GameState.PLAYING:
                self._update(dt)
            
            # Render
            self._render()
            
            # Cap framerate
            self.clock.tick(TARGET_FPS)
            
            # Update FPS counter
            self._update_fps()
        
        # Cleanup
        self._cleanup()
    
    def _process_input(self):
        """Process all input events."""
        events = self.input_handler.process_events()
        
        # Handle state-specific input
        if self.state == GameState.PLAYING:
            self._process_playing_input()
        elif self.state == GameState.PAUSED:
            self._process_paused_input()
    
    def _process_playing_input(self):
        """Process input while playing."""
        # Get mouse input for camera
        mouse_rel = self.input_handler.get_mouse_relative()
        
        # Update player input
        keys = {}
        for action in [InputAction.MOVE_FORWARD, InputAction.MOVE_BACKWARD,
                      InputAction.MOVE_LEFT, InputAction.MOVE_RIGHT,
                      InputAction.JUMP, InputAction.CROUCH, InputAction.SPRINT]:
            keys[self._action_to_pygame_key(action)] = \
                self.input_handler.is_action_pressed(action)
        
        self.player.handle_input(keys, mouse_rel)
        
        # Handle block interactions
        if self.input_handler.is_action_pressed(InputAction.BREAK_BLOCK):
            self.player.break_block(self.world_manager.chunk_manager)
        
        if self.input_handler.is_action_pressed(InputAction.PLACE_BLOCK):
            self.player.place_block(self.world_manager.chunk_manager)
        
        # Handle hotbar selection
        hotbar_slot = self.input_handler.get_hotbar_selection()
        if hotbar_slot is not None:
            self.player.selected_slot = hotbar_slot
        
        # Mouse wheel for hotbar
        if self.input_handler.is_action_pressed(InputAction.ZOOM_IN):
            self.player.handle_mouse_scroll(-1)
        elif self.input_handler.is_action_pressed(InputAction.ZOOM_OUT):
            self.player.handle_mouse_scroll(1)
    
    def _process_paused_input(self):
        """Process input while paused."""
        # Only handle unpause
        pass
    
    def _action_to_pygame_key(self, action: InputAction) -> int:
        """Convert action to pygame key constant."""
        # This is a temporary mapping for player input compatibility
        action_key_map = {
            InputAction.MOVE_FORWARD: pygame.K_w,
            InputAction.MOVE_BACKWARD: pygame.K_s,
            InputAction.MOVE_LEFT: pygame.K_a,
            InputAction.MOVE_RIGHT: pygame.K_d,
            InputAction.JUMP: pygame.K_SPACE,
            InputAction.CROUCH: pygame.K_LCTRL,
            InputAction.SPRINT: pygame.K_LSHIFT,
        }
        return action_key_map.get(action, 0)
    
    def _update(self, dt: float):
        """Update game logic."""
        # Update time of day
        self.day_time += dt / self.day_length
        if self.day_time >= 1.0:
            self.day_time -= 1.0
        
        # Update player physics
        self.player.update(dt, self.world_manager.chunk_manager)
        
        # Update camera
        camera_pos = self.player.get_camera_position()
        self.renderer.update_camera(camera_pos, self.player.rotation)
        
        # Update world
        self.world_manager.update(self.player.position, dt)
        
        # Update chunk meshes
        self._update_chunk_meshes()
    
    def _update_chunk_meshes(self):
        """Update meshes for dirty chunks."""
        chunks_updated = 0
        max_updates_per_frame = 2
        
        # Get chunks near player
        player_chunk = self.world_manager._queue_nearby_chunks(
            (int(self.player.position.x // 16),
             int(self.player.position.y // 16),
             int(self.player.position.z // 16))
        )
        
        # Update dirty chunks
        for chunk in self.world_manager.chunk_manager.chunks.values():
            if chunks_updated >= max_updates_per_frame:
                break
            
            if chunk.is_dirty and chunk.state == ChunkState.GENERATED:
                self.renderer.create_chunk_mesh(chunk)
                chunks_updated += 1
    
    def _render(self):
        """Render the game."""
        # Get visible chunks
        visible_chunks = list(self.world_manager.chunk_manager.chunks.values())
        
        # Render world
        self.renderer.render(visible_chunks, self.day_time)
        
        # Render HUD
        if self.state == GameState.PLAYING:
            self._render_hud()
        
        # Render debug info
        if self.show_debug:
            self._render_debug_info()
    
    def _render_hud(self):
        """Render the heads-up display."""
        # For now, just render crosshair using OpenGL
        # In a full implementation, this would include hotbar, health, etc.
        pass
    
    def _render_debug_info(self):
        """Render debug information."""
        # This would render FPS, coordinates, chunk info, etc.
        # For now, just print to console periodically
        if self.frame_count % 60 == 0:
            stats = self.world_manager.get_statistics()
            render_stats = self.renderer.render_stats
            
            print(f"\n--- Debug Info ---")
            print(f"FPS: {self.current_fps:.1f}")
            print(f"Position: {self.player.position}")
            print(f"Chunks: {stats['chunks_loaded']} loaded, "
                  f"{stats['chunks_generating']} generating")
            print(f"Rendering: {render_stats.chunks_rendered} chunks, "
                  f"{render_stats.chunks_culled} culled")
            print(f"Memory: {stats['memory_usage_mb']:.1f} MB")
    
    def _update_fps(self):
        """Update FPS counter."""
        self.frame_count += 1
        
        if self.current_time - self.last_fps_update >= 1.0:
            self.current_fps = self.frame_count / (self.current_time - self.last_fps_update)
            self.frame_count = 0
            self.last_fps_update = self.current_time
            
            # Update FPS history
            self.fps_history.append(self.current_fps)
            if len(self.fps_history) > 60:
                self.fps_history.pop(0)
    
    def _toggle_debug(self):
        """Toggle debug display."""
        self.show_debug = not self.show_debug
        print(f"Debug display: {'ON' if self.show_debug else 'OFF'}")
    
    def _toggle_wireframe(self):
        """Toggle wireframe rendering."""
        self.show_wireframe = not self.show_wireframe
        self.renderer.wireframe = self.show_wireframe
        print(f"Wireframe: {'ON' if self.show_wireframe else 'OFF'}")
    
    def _toggle_pause(self):
        """Toggle pause state."""
        if self.state == GameState.PLAYING:
            self.state = GameState.PAUSED
            self.input_handler.capture_mouse(False)
            print("Game paused")
        elif self.state == GameState.PAUSED:
            self.state = GameState.PLAYING
            self.input_handler.capture_mouse(True)
            print("Game resumed")
    
    def quit(self):
        """Quit the game."""
        print("\nShutting down...")
        self.running = False
    
    def _cleanup(self):
        """Clean up resources."""
        print("Cleaning up resources...")
        
        # Save world
        print("- Saving world...")
        self.world_manager.save_world()
        
        # Stop world generation
        print("- Stopping world generation...")
        self.world_manager.stop()
        
        # Clean up renderer
        print("- Cleaning up renderer...")
        self.renderer.cleanup()
        
        # Quit pygame
        pygame.quit()
        sys.exit(0)


def main():
    """Main entry point."""
    print("=== Minecraft Clone ===")
    print("A Python implementation with Pygame and OpenGL")
    print()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Minecraft Clone")
    parser.add_argument("--seed", type=int, help="World seed")
    parser.add_argument("--world", type=str, help="World name to load")
    args = parser.parse_args()
    
    # Create and run game
    game = MinecraftGame()
    
    try:
        # Initialize with seed or load world
        if args.world:
            print(f"Loading world: {args.world}")
            # TODO: Implement world loading
            game.initialize()
        else:
            seed = args.seed
            if seed is not None:
                print(f"Using seed: {seed}")
            game.initialize(seed)
        
        # Run the game
        game.run()
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        pygame.quit()
        sys.exit(1)


if __name__ == "__main__":
    main()