"""
Noise and terrain generation for the Minecraft clone.

This module implements OpenSimplex noise and various terrain generation
algorithms for creating realistic voxel landscapes.
"""

import numpy as np
from typing import Tuple, Optional, Dict, List
from numba import jit, prange
import math
from enum import Enum
from dataclasses import dataclass

from config import (
    BlockType, TERRAIN_SCALE, TERRAIN_OCTAVES, TERRAIN_PERSISTENCE,
    TERRAIN_LACUNARITY, SEA_LEVEL, MAX_HEIGHT, CAVE_THRESHOLD,
    CAVE_SCALE, CAVE_OCTAVES, BIOME_SCALE, MOISTURE_SCALE,
    CHUNK_SIZE
)


class BiomeType(Enum):
    """Different biome types in the world."""
    OCEAN = 0
    BEACH = 1
    PLAINS = 2
    FOREST = 3
    DESERT = 4
    MOUNTAINS = 5
    SNOW = 6
    SWAMP = 7
    TAIGA = 8


@dataclass
class BiomeProperties:
    """Properties defining a biome."""
    name: str
    surface_block: BlockType
    subsurface_block: BlockType
    stone_block: BlockType
    tree_density: float
    grass_density: float
    min_elevation: float
    max_elevation: float
    temperature: float
    moisture: float


# Biome definitions
BIOME_PROPERTIES = {
    BiomeType.OCEAN: BiomeProperties(
        "Ocean", BlockType.SAND, BlockType.SAND, BlockType.STONE,
        0.0, 0.0, -1.0, 0.3, 0.5, 0.8
    ),
    BiomeType.BEACH: BiomeProperties(
        "Beach", BlockType.SAND, BlockType.SAND, BlockType.STONE,
        0.02, 0.05, 0.3, 0.35, 0.7, 0.5
    ),
    BiomeType.PLAINS: BiomeProperties(
        "Plains", BlockType.GRASS, BlockType.DIRT, BlockType.STONE,
        0.01, 0.3, 0.35, 0.6, 0.6, 0.4
    ),
    BiomeType.FOREST: BiomeProperties(
        "Forest", BlockType.GRASS, BlockType.DIRT, BlockType.STONE,
        0.3, 0.4, 0.35, 0.7, 0.5, 0.6
    ),
    BiomeType.DESERT: BiomeProperties(
        "Desert", BlockType.SAND, BlockType.SAND, BlockType.STONE,
        0.001, 0.02, 0.35, 0.6, 0.9, 0.1
    ),
    BiomeType.MOUNTAINS: BiomeProperties(
        "Mountains", BlockType.STONE, BlockType.STONE, BlockType.STONE,
        0.05, 0.1, 0.6, 1.0, 0.3, 0.3
    ),
    BiomeType.SNOW: BiomeProperties(
        "Snow", BlockType.GRASS, BlockType.DIRT, BlockType.STONE,
        0.1, 0.05, 0.7, 1.0, 0.0, 0.5
    ),
    BiomeType.SWAMP: BiomeProperties(
        "Swamp", BlockType.GRASS, BlockType.DIRT, BlockType.STONE,
        0.2, 0.6, 0.3, 0.4, 0.7, 0.9
    ),
    BiomeType.TAIGA: BiomeProperties(
        "Taiga", BlockType.GRASS, BlockType.DIRT, BlockType.STONE,
        0.25, 0.1, 0.4, 0.7, 0.2, 0.6
    ),
}


class OpenSimplexNoise:
    """
    OpenSimplex noise implementation for terrain generation.
    
    This is a simplified 2D/3D implementation suitable for voxel terrain.
    """
    
    def __init__(self, seed: int = 0):
        """Initialize noise generator with a seed."""
        self.seed = seed
        self.perm = self._generate_permutation(seed)
        
        # Gradients for 2D
        self.grad2 = np.array([
            [1, 1], [-1, 1], [1, -1], [-1, -1],
            [1, 0], [-1, 0], [0, 1], [0, -1]
        ], dtype=np.float32) / np.sqrt(2)
        
        # Gradients for 3D
        self.grad3 = np.array([
            [1, 1, 0], [-1, 1, 0], [1, -1, 0], [-1, -1, 0],
            [1, 0, 1], [-1, 0, 1], [1, 0, -1], [-1, 0, -1],
            [0, 1, 1], [0, -1, 1], [0, 1, -1], [0, -1, -1]
        ], dtype=np.float32) / np.sqrt(2)
    
    def _generate_permutation(self, seed: int) -> np.ndarray:
        """Generate permutation table from seed."""
        np.random.seed(seed)
        perm = np.arange(256, dtype=np.int32)
        np.random.shuffle(perm)
        return np.tile(perm, 2)
    
    @jit(nopython=True)
    def _extrapolate2(self, xsb: int, ysb: int, dx: float, dy: float,
                      perm: np.ndarray, grad2: np.ndarray) -> float:
        """Extrapolate gradient for 2D noise."""
        index = perm[(perm[xsb & 0xFF] + ysb) & 0xFF] & 0x07
        g = grad2[index]
        return g[0] * dx + g[1] * dy
    
    def noise2(self, x: float, y: float) -> float:
        """
        Generate 2D OpenSimplex noise.
        
        Args:
            x, y: Input coordinates
            
        Returns:
            Noise value in range [-1, 1]
        """
        # Skew the input space to determine which simplex cell we're in
        F2 = 0.5 * (np.sqrt(3.0) - 1.0)
        s = (x + y) * F2
        i = int(np.floor(x + s))
        j = int(np.floor(y + s))
        
        G2 = (3.0 - np.sqrt(3.0)) / 6.0
        t = (i + j) * G2
        X0 = i - t
        Y0 = j - t
        x0 = x - X0
        y0 = y - Y0
        
        # Determine which simplex we're in
        if x0 > y0:
            i1, j1 = 1, 0
        else:
            i1, j1 = 0, 1
        
        x1 = x0 - i1 + G2
        y1 = y0 - j1 + G2
        x2 = x0 - 1.0 + 2.0 * G2
        y2 = y0 - 1.0 + 2.0 * G2
        
        # Calculate contributions from three corners
        n0 = n1 = n2 = 0.0
        
        t0 = 0.5 - x0 * x0 - y0 * y0
        if t0 >= 0:
            t0 *= t0
            n0 = t0 * t0 * self._extrapolate2(i, j, x0, y0, self.perm, self.grad2)
        
        t1 = 0.5 - x1 * x1 - y1 * y1
        if t1 >= 0:
            t1 *= t1
            n1 = t1 * t1 * self._extrapolate2(i + i1, j + j1, x1, y1, 
                                             self.perm, self.grad2)
        
        t2 = 0.5 - x2 * x2 - y2 * y2
        if t2 >= 0:
            t2 *= t2
            n2 = t2 * t2 * self._extrapolate2(i + 1, j + 1, x2, y2, 
                                             self.perm, self.grad2)
        
        # Scale result to [-1, 1]
        return 70.0 * (n0 + n1 + n2)
    
    def noise3(self, x: float, y: float, z: float) -> float:
        """
        Generate 3D OpenSimplex noise.
        
        Args:
            x, y, z: Input coordinates
            
        Returns:
            Noise value in range [-1, 1]
        """
        # Simplified 3D implementation
        # For full implementation, we'd need the complete 3D simplex algorithm
        # This is a reasonable approximation using layered 2D noise
        return (self.noise2(x, y) + self.noise2(x + 17.0, z) + 
                self.noise2(y + 31.0, z + 7.0)) / 3.0
    
    def octave_noise2(self, x: float, y: float, octaves: int,
                      persistence: float, lacunarity: float) -> float:
        """
        Generate fractal noise using multiple octaves.
        
        Args:
            x, y: Input coordinates
            octaves: Number of noise layers
            persistence: Amplitude multiplier per octave
            lacunarity: Frequency multiplier per octave
            
        Returns:
            Combined noise value
        """
        value = 0.0
        amplitude = 1.0
        frequency = 1.0
        max_value = 0.0
        
        for _ in range(octaves):
            value += amplitude * self.noise2(x * frequency, y * frequency)
            max_value += amplitude
            amplitude *= persistence
            frequency *= lacunarity
        
        return value / max_value


class TerrainGenerator:
    """Generates terrain using multiple noise layers and biomes."""
    
    def __init__(self, seed: int = 0):
        """Initialize terrain generator with seed."""
        self.seed = seed
        self.height_noise = OpenSimplexNoise(seed)
        self.moisture_noise = OpenSimplexNoise(seed + 1)
        self.temperature_noise = OpenSimplexNoise(seed + 2)
        self.cave_noise = OpenSimplexNoise(seed + 3)
        self.ore_noise = OpenSimplexNoise(seed + 4)
    
    def get_biome(self, x: int, z: int) -> BiomeType:
        """
        Determine biome at given coordinates.
        
        Args:
            x, z: World coordinates
            
        Returns:
            Biome type at location
        """
        # Sample biome parameters
        elevation = self.get_base_height(x, z) / MAX_HEIGHT
        moisture = self.moisture_noise.octave_noise2(
            x * MOISTURE_SCALE, z * MOISTURE_SCALE, 4, 0.5, 2.0
        ) * 0.5 + 0.5
        temperature = self.temperature_noise.octave_noise2(
            x * BIOME_SCALE * 2, z * BIOME_SCALE * 2, 3, 0.5, 2.0
        ) * 0.5 + 0.5
        
        # Determine biome based on parameters
        if elevation < 0.3:
            return BiomeType.OCEAN
        elif elevation < 0.35:
            return BiomeType.BEACH
        elif elevation > 0.7:
            if temperature < 0.3:
                return BiomeType.SNOW
            else:
                return BiomeType.MOUNTAINS
        else:
            # Mid elevations - use moisture and temperature
            if temperature > 0.7:
                if moisture < 0.3:
                    return BiomeType.DESERT
                else:
                    return BiomeType.SWAMP
            elif temperature < 0.3:
                return BiomeType.TAIGA
            else:
                if moisture > 0.6:
                    return BiomeType.FOREST
                else:
                    return BiomeType.PLAINS
    
    def get_base_height(self, x: int, z: int) -> int:
        """
        Get base terrain height at coordinates.
        
        Args:
            x, z: World coordinates
            
        Returns:
            Height in blocks
        """
        # Continental noise - large scale features
        continental = self.height_noise.octave_noise2(
            x * TERRAIN_SCALE * 0.5, z * TERRAIN_SCALE * 0.5,
            4, 0.5, 2.0
        )
        
        # Regional noise - medium scale features
        regional = self.height_noise.octave_noise2(
            x * TERRAIN_SCALE, z * TERRAIN_SCALE,
            TERRAIN_OCTAVES, TERRAIN_PERSISTENCE, TERRAIN_LACUNARITY
        )
        
        # Local noise - small scale details
        local = self.height_noise.octave_noise2(
            x * TERRAIN_SCALE * 4, z * TERRAIN_SCALE * 4,
            3, 0.6, 2.0
        ) * 0.25
        
        # Combine noise layers
        height = (continental * 0.4 + regional * 0.4 + local * 0.2)
        
        # Transform from [-1, 1] to [0, MAX_HEIGHT]
        height = (height + 1.0) * 0.5 * (MAX_HEIGHT - SEA_LEVEL) + SEA_LEVEL
        
        return int(height)
    
    def has_cave(self, x: int, y: int, z: int) -> bool:
        """
        Check if there's a cave at given position.
        
        Args:
            x, y, z: World coordinates
            
        Returns:
            True if position should be empty (cave)
        """
        # 3D cave noise
        density = self.cave_noise.noise3(
            x * CAVE_SCALE, y * CAVE_SCALE, z * CAVE_SCALE
        )
        
        # Additional worm caves
        worm_cave = self.cave_noise.octave_noise2(
            x * CAVE_SCALE * 2, z * CAVE_SCALE * 2,
            CAVE_OCTAVES, 0.5, 2.0
        )
        
        # Adjust threshold based on depth
        depth_factor = 1.0 - (y / MAX_HEIGHT)
        threshold = CAVE_THRESHOLD - depth_factor * 0.1
        
        return density > threshold or abs(worm_cave) < 0.1
    
    @jit(nopython=True, parallel=True)
    def generate_chunk_heightmap(self, chunk_x: int, chunk_z: int,
                                chunk_size: int) -> np.ndarray:
        """
        Generate heightmap for a chunk.
        
        Args:
            chunk_x, chunk_z: Chunk coordinates
            chunk_size: Size of chunk
            
        Returns:
            2D array of heights
        """
        heightmap = np.zeros((chunk_size, chunk_size), dtype=np.int32)
        base_x = chunk_x * chunk_size
        base_z = chunk_z * chunk_size
        
        for x in prange(chunk_size):
            for z in range(chunk_size):
                world_x = base_x + x
                world_z = base_z + z
                heightmap[x, z] = self.get_base_height(world_x, world_z)
        
        return heightmap
    
    def generate_chunk_blocks(self, chunk_x: int, chunk_y: int, chunk_z: int,
                            chunk_size: int) -> np.ndarray:
        """
        Generate all blocks for a chunk.
        
        Args:
            chunk_x, chunk_y, chunk_z: Chunk coordinates
            chunk_size: Size of chunk
            
        Returns:
            3D array of block types
        """
        blocks = np.zeros((chunk_size, chunk_size, chunk_size), dtype=np.uint8)
        base_x = chunk_x * chunk_size
        base_y = chunk_y * chunk_size
        base_z = chunk_z * chunk_size
        
        # Generate heightmap first
        heightmap = self.generate_chunk_heightmap(chunk_x, chunk_z, chunk_size)
        
        for x in range(chunk_size):
            for z in range(chunk_size):
                world_x = base_x + x
                world_z = base_z + z
                height = heightmap[x, z]
                biome = self.get_biome(world_x, world_z)
                biome_props = BIOME_PROPERTIES[biome]
                
                for y in range(chunk_size):
                    world_y = base_y + y
                    
                    if world_y > height:
                        # Above ground - check for water
                        if world_y <= SEA_LEVEL:
                            blocks[x, y, z] = BlockType.WATER.value
                        else:
                            blocks[x, y, z] = BlockType.AIR.value
                    else:
                        # Underground - check for caves
                        if self.has_cave(world_x, world_y, world_z):
                            blocks[x, y, z] = BlockType.AIR.value
                        else:
                            # Determine block type based on depth and biome
                            depth = height - world_y
                            if depth == 0:
                                blocks[x, y, z] = biome_props.surface_block.value
                            elif depth < 4:
                                blocks[x, y, z] = biome_props.subsurface_block.value
                            else:
                                # Stone with possible ores
                                blocks[x, y, z] = self._get_ore_type(
                                    world_x, world_y, world_z,
                                    biome_props.stone_block
                                ).value
        
        return blocks
    
    def _get_ore_type(self, x: int, y: int, z: int,
                      base_stone: BlockType) -> BlockType:
        """
        Determine if position contains ore.
        
        Args:
            x, y, z: World coordinates
            base_stone: Default stone type
            
        Returns:
            Block type (ore or stone)
        """
        # Ore generation based on depth and noise
        ore_value = self.ore_noise.noise3(x * 0.1, y * 0.1, z * 0.1)
        
        # Different ores at different depths
        if y < 16:  # Deep ores
            if ore_value > 0.95:
                return BlockType.DIAMOND_ORE
            elif ore_value > 0.9:
                return BlockType.GOLD_ORE
        elif y < 32:  # Mid-level ores
            if ore_value > 0.92:
                return BlockType.GOLD_ORE
            elif ore_value > 0.88:
                return BlockType.IRON_ORE
        elif y < 64:  # Common ores
            if ore_value > 0.9:
                return BlockType.IRON_ORE
            elif ore_value > 0.85:
                return BlockType.COAL_ORE
        
        return base_stone
    
    def generate_structures(self, chunk_x: int, chunk_z: int,
                          blocks: np.ndarray, biomes: Dict[Tuple[int, int], BiomeType]):
        """
        Add structures like trees to a chunk.
        
        Args:
            chunk_x, chunk_z: Chunk coordinates
            blocks: Chunk block array to modify
            biomes: Biome map for chunk positions
        """
        # Tree generation based on biome
        for x in range(2, CHUNK_SIZE - 2):
            for z in range(2, CHUNK_SIZE - 2):
                biome = biomes.get((x, z), BiomeType.PLAINS)
                biome_props = BIOME_PROPERTIES[biome]
                
                # Check if we should place a tree
                tree_chance = self.height_noise.noise2(
                    (chunk_x * CHUNK_SIZE + x) * 0.1,
                    (chunk_z * CHUNK_SIZE + z) * 0.1
                )
                
                if tree_chance > (1.0 - biome_props.tree_density):
                    # Find ground level
                    for y in range(CHUNK_SIZE - 1, -1, -1):
                        if blocks[x, y, z] != BlockType.AIR.value:
                            if blocks[x, y, z] == BlockType.GRASS.value:
                                self._place_tree(blocks, x, y + 1, z, biome)
                            break
    
    def _place_tree(self, blocks: np.ndarray, x: int, y: int, z: int,
                    biome: BiomeType):
        """Place a tree at given position."""
        # Simple tree generation
        tree_height = np.random.randint(4, 7)
        
        # Check if there's space
        if y + tree_height + 2 >= CHUNK_SIZE:
            return
        
        # Place trunk
        for h in range(tree_height):
            if y + h < CHUNK_SIZE:
                blocks[x, y + h, z] = BlockType.WOOD.value
        
        # Place leaves
        leaf_start = y + tree_height - 2
        for ly in range(3):
            leaf_y = leaf_start + ly
            if leaf_y >= CHUNK_SIZE:
                continue
            
            radius = 2 - ly // 2
            for lx in range(-radius, radius + 1):
                for lz in range(-radius, radius + 1):
                    if (0 <= x + lx < CHUNK_SIZE and 
                        0 <= z + lz < CHUNK_SIZE):
                        if abs(lx) + abs(lz) <= radius + 1:
                            if blocks[x + lx, leaf_y, z + lz] == BlockType.AIR.value:
                                blocks[x + lx, leaf_y, z + lz] = BlockType.LEAVES.value
