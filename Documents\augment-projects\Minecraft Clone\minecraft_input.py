"""
Input handling system for the Minecraft clone.

This module centralizes keyboard and mouse input processing.
"""

import pygame
from pygame.locals import *
from typing import Dict, Set, List, Tuple, Optional, Callable
from dataclasses import dataclass
from enum import Enum, auto


class InputAction(Enum):
    """All possible input actions in the game."""
    # Movement
    MOVE_FORWARD = auto()
    MOVE_BACKWARD = auto()
    MOVE_LEFT = auto()
    MOVE_RIGHT = auto()
    JUMP = auto()
    CROUCH = auto()
    SPRINT = auto()
    
    # Interaction
    BREAK_BLOCK = auto()
    PLACE_BLOCK = auto()
    
    # Inventory
    HOTBAR_1 = auto()
    HOTBAR_2 = auto()
    HOTBAR_3 = auto()
    HOTBAR_4 = auto()
    HOTBAR_5 = auto()
    HOTBAR_6 = auto()
    HOTBAR_7 = auto()
    HOTBAR_8 = auto()
    HOTBAR_9 = auto()
    
    # Game controls
    TOGGLE_INVENTORY = auto()
    TOGGLE_FLYING = auto()
    TOGGLE_DEBUG = auto()
    TOGGLE_WIREFRAME = auto()
    TOGGLE_FULLSCREEN = auto()
    
    # Camera
    ZOOM_IN = auto()
    ZOOM_OUT = auto()
    
    # Menu
    PAUSE = auto()
    QUIT = auto()


@dataclass
class InputEvent:
    """Represents an input event."""
    action: InputAction
    pressed: bool
    time: float
    modifiers: int = 0


class InputHandler:
    """Handles all game input and converts to actions."""
    
    def __init__(self):
        """Initialize input handler."""
        # Key bindings
        self.key_bindings: Dict[int, InputAction] = {
            # Movement
            K_w: InputAction.MOVE_FORWARD,
            K_s: InputAction.MOVE_BACKWARD,
            K_a: InputAction.MOVE_LEFT,
            K_d: InputAction.MOVE_RIGHT,
            K_SPACE: InputAction.JUMP,
            K_LCTRL: InputAction.CROUCH,
            K_LSHIFT: InputAction.SPRINT,
            
            # Hotbar
            K_1: InputAction.HOTBAR_1,
            K_2: InputAction.HOTBAR_2,
            K_3: InputAction.HOTBAR_3,
            K_4: InputAction.HOTBAR_4,
            K_5: InputAction.HOTBAR_5,
            K_6: InputAction.HOTBAR_6,
            K_7: InputAction.HOTBAR_7,
            K_8: InputAction.HOTBAR_8,
            K_9: InputAction.HOTBAR_9,
            
            # Game controls
            K_e: InputAction.TOGGLE_INVENTORY,
            K_f: InputAction.TOGGLE_FLYING,
            K_F3: InputAction.TOGGLE_DEBUG,
            K_F4: InputAction.TOGGLE_WIREFRAME,
            K_F11: InputAction.TOGGLE_FULLSCREEN,
            
            # Menu
            K_ESCAPE: InputAction.PAUSE,
        }
        
        # Mouse button bindings
        self.mouse_bindings: Dict[int, InputAction] = {
            1: InputAction.BREAK_BLOCK,  # Left click
            3: InputAction.PLACE_BLOCK,  # Right click
        }
        
        # Current input state
        self.keys_pressed: Set[int] = set()
        self.mouse_buttons_pressed: Set[int] = set()
        self.mouse_position: Tuple[int, int] = (0, 0)
        self.mouse_relative: Tuple[int, int] = (0, 0)
        
        # Action states
        self.action_states: Dict[InputAction, bool] = {
            action: False for action in InputAction
        }
        
        # Event queue
        self.events: List[InputEvent] = []
        
        # Callbacks
        self.action_callbacks: Dict[InputAction, List[Callable]] = {
            action: [] for action in InputAction
        }
        
        # Mouse settings
        self.mouse_captured = False
        self.mouse_sensitivity = 1.0
    
    def process_events(self) -> List[InputEvent]:
        """
        Process pygame events and convert to input events.
        
        Returns:
            List of input events this frame
        """
        self.events.clear()
        self.mouse_relative = (0, 0)
        
        current_time = pygame.time.get_ticks() / 1000.0
        
        for event in pygame.event.get():
            if event.type == QUIT:
                self._add_event(InputAction.QUIT, True, current_time)
            
            elif event.type == KEYDOWN:
                self._handle_key_down(event.key, event.mod, current_time)
            
            elif event.type == KEYUP:
                self._handle_key_up(event.key, event.mod, current_time)
            
            elif event.type == MOUSEBUTTONDOWN:
                self._handle_mouse_down(event.button, current_time)
            
            elif event.type == MOUSEBUTTONUP:
                self._handle_mouse_up(event.button, current_time)
            
            elif event.type == MOUSEMOTION:
                self._handle_mouse_motion(event.pos, event.rel)
            
            elif event.type == MOUSEWHEEL:
                self._handle_mouse_wheel(event.y, current_time)
        
        return self.events
    
    def _handle_key_down(self, key: int, mod: int, time: float):
        """Handle key press event."""
        if key in self.keys_pressed:
            return  # Ignore repeat events
        
        self.keys_pressed.add(key)
        
        # Check for bound action
        if key in self.key_bindings:
            action = self.key_bindings[key]
            self.action_states[action] = True
            self._add_event(action, True, time, mod)
    
    def _handle_key_up(self, key: int, mod: int, time: float):
        """Handle key release event."""
        self.keys_pressed.discard(key)
        
        # Check for bound action
        if key in self.key_bindings:
            action = self.key_bindings[key]
            self.action_states[action] = False
            self._add_event(action, False, time, mod)
    
    def _handle_mouse_down(self, button: int, time: float):
        """Handle mouse button press."""
        self.mouse_buttons_pressed.add(button)
        
        if button in self.mouse_bindings:
            action = self.mouse_bindings[button]
            self.action_states[action] = True
            self._add_event(action, True, time)
    
    def _handle_mouse_up(self, button: int, time: float):
        """Handle mouse button release."""
        self.mouse_buttons_pressed.discard(button)
        
        if button in self.mouse_bindings:
            action = self.mouse_bindings[button]
            self.action_states[action] = False
            self._add_event(action, False, time)
    
    def _handle_mouse_motion(self, pos: Tuple[int, int], rel: Tuple[int, int]):
        """Handle mouse movement."""
        self.mouse_position = pos
        
        if self.mouse_captured:
            # Apply sensitivity
            self.mouse_relative = (
                rel[0] * self.mouse_sensitivity,
                rel[1] * self.mouse_sensitivity
            )
    
    def _handle_mouse_wheel(self, y: int, time: float):
        """Handle mouse wheel."""
        if y > 0:
            self._add_event(InputAction.ZOOM_IN, True, time)
            self._add_event(InputAction.ZOOM_IN, False, time)
        elif y < 0:
            self._add_event(InputAction.ZOOM_OUT, True, time)
            self._add_event(InputAction.ZOOM_OUT, False, time)
    
    def _add_event(self, action: InputAction, pressed: bool, 
                   time: float, modifiers: int = 0):
        """Add an input event and trigger callbacks."""
        event = InputEvent(action, pressed, time, modifiers)
        self.events.append(event)
        
        # Trigger callbacks
        if pressed:  # Only on press, not release
            for callback in self.action_callbacks[action]:
                callback(event)
    
    def is_action_pressed(self, action: InputAction) -> bool:
        """Check if an action is currently pressed."""
        return self.action_states.get(action, False)
    
    def is_key_pressed(self, key: int) -> bool:
        """Check if a specific key is pressed."""
        return key in self.keys_pressed
    
    def is_mouse_button_pressed(self, button: int) -> bool:
        """Check if a mouse button is pressed."""
        return button in self.mouse_buttons_pressed
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        return self.mouse_position
    
    def get_mouse_relative(self) -> Tuple[int, int]:
        """Get relative mouse movement this frame."""
        return self.mouse_relative
    
    def capture_mouse(self, capture: bool = True):
        """Enable or disable mouse capture."""
        self.mouse_captured = capture
        pygame.mouse.set_visible(not capture)
        pygame.event.set_grab(capture)
        
        if capture:
            # Center mouse
            window_size = pygame.display.get_surface().get_size()
            pygame.mouse.set_pos(window_size[0] // 2, window_size[1] // 2)
    
    def set_mouse_sensitivity(self, sensitivity: float):
        """Set mouse sensitivity multiplier."""
        self.mouse_sensitivity = max(0.1, min(10.0, sensitivity))
    
    def bind_key(self, key: int, action: InputAction):
        """Bind a key to an action."""
        # Remove old binding if exists
        old_action = self.key_bindings.get(key)
        if old_action:
            self.action_states[old_action] = False
        
        self.key_bindings[key] = action
    
    def unbind_key(self, key: int):
        """Remove a key binding."""
        if key in self.key_bindings:
            action = self.key_bindings[key]
            self.action_states[action] = False
            del self.key_bindings[key]
    
    def bind_mouse_button(self, button: int, action: InputAction):
        """Bind a mouse button to an action."""
        self.mouse_bindings[button] = action
    
    def register_callback(self, action: InputAction, callback: Callable):
        """Register a callback for an action."""
        self.action_callbacks[action].append(callback)
    
    def unregister_callback(self, action: InputAction, callback: Callable):
        """Unregister a callback for an action."""
        if callback in self.action_callbacks[action]:
            self.action_callbacks[action].remove(callback)
    
    def get_movement_vector(self) -> Tuple[float, float]:
        """
        Get normalized movement vector from input.
        
        Returns:
            (forward, right) movement values
        """
        forward = 0.0
        right = 0.0
        
        if self.is_action_pressed(InputAction.MOVE_FORWARD):
            forward += 1.0
        if self.is_action_pressed(InputAction.MOVE_BACKWARD):
            forward -= 1.0
        if self.is_action_pressed(InputAction.MOVE_LEFT):
            right -= 1.0
        if self.is_action_pressed(InputAction.MOVE_RIGHT):
            right += 1.0
        
        # Normalize diagonal movement
        if forward != 0 and right != 0:
            magnitude = (forward ** 2 + right ** 2) ** 0.5
            forward /= magnitude
            right /= magnitude
        
        return forward, right
    
    def reset(self):
        """Reset all input states."""
        self.keys_pressed.clear()
        self.mouse_buttons_pressed.clear()
        self.mouse_relative = (0, 0)
        self.events.clear()
        
        for action in InputAction:
            self.action_states[action] = False
    
    def get_hotbar_selection(self) -> Optional[int]:
        """
        Get selected hotbar slot from input.
        
        Returns:
            Slot index (0-8) or None if no selection
        """
        hotbar_actions = [
            (InputAction.HOTBAR_1, 0),
            (InputAction.HOTBAR_2, 1),
            (InputAction.HOTBAR_3, 2),
            (InputAction.HOTBAR_4, 3),
            (InputAction.HOTBAR_5, 4),
            (InputAction.HOTBAR_6, 5),
            (InputAction.HOTBAR_7, 6),
            (InputAction.HOTBAR_8, 7),
            (InputAction.HOTBAR_9, 8),
        ]
        
        for action, slot in hotbar_actions:
            if self.is_action_pressed(action):
                return slot
        
        return None