"""
Chunk and block storage system for the Minecraft clone.

This module provides efficient data structures for storing and accessing
voxel data, including compression and memory optimization.
"""

import numpy as np
from typing import Dict, Tuple, Optional, List, Set
from dataclasses import dataclass
import struct
import zlib
from enum import Enum
import threading
from collections import OrderedDict

from config import BlockType, CHUNK_SIZE, Face, NEIGHBOR_OFFSETS
from math_utils import Vec3, AABB


class ChunkState(Enum):
    """State of a chunk in the loading pipeline."""
    UNLOADED = 0
    GENERATING = 1
    GENERATED = 2
    MESHING = 3
    READY = 4
    UNLOADING = 5


@dataclass
class BlockUpdate:
    """Represents a block update event."""
    chunk_pos: Tuple[int, int, int]
    local_pos: Tuple[int, int, int]
    old_type: BlockType
    new_type: BlockType
    timestamp: float


class Chunk:
    """
    Represents a 16x16x16 chunk of blocks.
    
    Optimized for memory efficiency and fast access.
    """
    
    def __init__(self, chunk_x: int, chunk_y: int, chunk_z: int):
        """
        Initialize chunk at given chunk coordinates.
        
        Args:
            chunk_x, chunk_y, chunk_z: Chunk position in chunk space
        """
        self.chunk_x = chunk_x
        self.chunk_y = chunk_y
        self.chunk_z = chunk_z
        self.world_x = chunk_x * CHUNK_SIZE
        self.world_y = chunk_y * CHUNK_SIZE
        self.world_z = chunk_z * CHUNK_SIZE
        
        # Block storage - using uint8 for memory efficiency
        self.blocks = np.zeros((CHUNK_SIZE, CHUNK_SIZE, CHUNK_SIZE), dtype=np.uint8)
        
        # Chunk state
        self.state = ChunkState.UNLOADED
        self.is_dirty = True  # Needs mesh rebuild
        self.is_empty = True
        self.is_full = False
        
        # Mesh data for rendering
        self.vertex_data = None
        self.vertex_count = 0
        self.face_count = 0
        
        # Neighbor chunks for seamless rendering
        self.neighbors: Dict[Face, Optional['Chunk']] = {
            face: None for face in Face
        }
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Compression data
        self.compressed_data = None
        self.compression_ratio = 1.0
        
        # Lighting data (simplified)
        self.light_data = np.full((CHUNK_SIZE, CHUNK_SIZE, CHUNK_SIZE), 
                                  15, dtype=np.uint8)
    
    def get_block(self, x: int, y: int, z: int) -> BlockType:
        """
        Get block type at local coordinates.
        
        Args:
            x, y, z: Local coordinates within chunk (0-15)
            
        Returns:
            Block type at position
        """
        if not (0 <= x < CHUNK_SIZE and 0 <= y < CHUNK_SIZE and 0 <= z < CHUNK_SIZE):
            return BlockType.AIR
        
        with self.lock:
            return BlockType(self.blocks[x, y, z])
    
    def set_block(self, x: int, y: int, z: int, block_type: BlockType) -> bool:
        """
        Set block type at local coordinates.
        
        Args:
            x, y, z: Local coordinates within chunk (0-15)
            block_type: Type of block to set
            
        Returns:
            True if block was changed
        """
        if not (0 <= x < CHUNK_SIZE and 0 <= y < CHUNK_SIZE and 0 <= z < CHUNK_SIZE):
            return False
        
        with self.lock:
            old_type = self.blocks[x, y, z]
            if old_type != block_type.value:
                self.blocks[x, y, z] = block_type.value
                self.is_dirty = True
                self._update_empty_full_status()
                
                # Mark neighboring chunks dirty if on edge
                if x == 0 and self.neighbors[Face.WEST]:
                    self.neighbors[Face.WEST].is_dirty = True
                elif x == CHUNK_SIZE - 1 and self.neighbors[Face.EAST]:
                    self.neighbors[Face.EAST].is_dirty = True
                
                if y == 0 and self.neighbors[Face.BOTTOM]:
                    self.neighbors[Face.BOTTOM].is_dirty = True
                elif y == CHUNK_SIZE - 1 and self.neighbors[Face.TOP]:
                    self.neighbors[Face.TOP].is_dirty = True
                
                if z == 0 and self.neighbors[Face.NORTH]:
                    self.neighbors[Face.NORTH].is_dirty = True
                elif z == CHUNK_SIZE - 1 and self.neighbors[Face.SOUTH]:
                    self.neighbors[Face.SOUTH].is_dirty = True
                
                return True
        
        return False
    
    def set_blocks(self, block_data: np.ndarray):
        """
        Set all blocks in chunk from array.
        
        Args:
            block_data: 3D numpy array of block types
        """
        with self.lock:
            self.blocks = block_data.astype(np.uint8)
            self.is_dirty = True
            self._update_empty_full_status()
    
    def _update_empty_full_status(self):
        """Update empty/full flags for optimization."""
        unique_blocks = np.unique(self.blocks)
        self.is_empty = len(unique_blocks) == 1 and unique_blocks[0] == BlockType.AIR.value
        self.is_full = len(unique_blocks) == 1 and unique_blocks[0] != BlockType.AIR.value
    
    def get_visible_faces(self) -> List[Tuple[int, int, int, Face, BlockType]]:
        """
        Get all visible block faces for mesh generation.
        
        Returns:
            List of (x, y, z, face, block_type) for visible faces
        """
        if self.is_empty:
            return []
        
        visible_faces = []
        
        with self.lock:
            for x in range(CHUNK_SIZE):
                for y in range(CHUNK_SIZE):
                    for z in range(CHUNK_SIZE):
                        block_type = BlockType(self.blocks[x, y, z])
                        
                        if block_type == BlockType.AIR:
                            continue
                        
                        # Check each face
                        for face, (dx, dy, dz) in zip(Face, NEIGHBOR_OFFSETS):
                            nx, ny, nz = x + dx, y + dy, z + dz
                            
                            # Check if face is visible
                            if self._is_face_visible(x, y, z, nx, ny, nz, face):
                                visible_faces.append((x, y, z, face, block_type))
        
        return visible_faces
    
    def _is_face_visible(self, x: int, y: int, z: int,
                        nx: int, ny: int, nz: int, face: Face) -> bool:
        """Check if a block face is visible."""
        # Check within chunk
        if 0 <= nx < CHUNK_SIZE and 0 <= ny < CHUNK_SIZE and 0 <= nz < CHUNK_SIZE:
            neighbor_type = BlockType(self.blocks[nx, ny, nz])
            from config import is_transparent
            current_type = BlockType(self.blocks[x, y, z])
            
            # Face is visible if neighbor is air or transparent (and not same type)
            return neighbor_type == BlockType.AIR or (
                is_transparent(neighbor_type) and neighbor_type != current_type
            )
        else:
            # Check neighbor chunk
            neighbor_chunk = self._get_neighbor_for_face(face)
            if neighbor_chunk:
                # Convert to neighbor chunk's local coordinates
                local_x = nx % CHUNK_SIZE
                local_y = ny % CHUNK_SIZE
                local_z = nz % CHUNK_SIZE
                neighbor_type = neighbor_chunk.get_block(local_x, local_y, local_z)
                current_type = BlockType(self.blocks[x, y, z])
                
                return neighbor_type == BlockType.AIR or (
                    is_transparent(neighbor_type) and neighbor_type != current_type
                )
            
            # No neighbor chunk - assume visible
            return True
    
    def _get_neighbor_for_face(self, face: Face) -> Optional['Chunk']:
        """Get the neighbor chunk for a given face."""
        return self.neighbors.get(face)
    
    def compress(self) -> bytes:
        """
        Compress chunk data for storage.
        
        Returns:
            Compressed chunk data
        """
        with self.lock:
            # Create header with chunk metadata
            header = struct.pack(
                '<iiiBB',  # Little-endian: 3 ints, 2 bytes
                self.chunk_x, self.chunk_y, self.chunk_z,
                1 if self.is_empty else 0,
                1 if self.is_full else 0
            )
            
            if self.is_empty:
                # Don't store block data for empty chunks
                self.compressed_data = header
                self.compression_ratio = float('inf')
                return self.compressed_data
            
            # Use zlib compression for block data
            block_bytes = self.blocks.tobytes()
            compressed_blocks = zlib.compress(block_bytes, level=6)
            
            self.compressed_data = header + compressed_blocks
            self.compression_ratio = len(block_bytes) / len(compressed_blocks)
            
            return self.compressed_data
    
    def decompress(self, data: bytes):
        """
        Decompress chunk data from storage.
        
        Args:
            data: Compressed chunk data
        """
        with self.lock:
            # Read header
            header_size = struct.calcsize('<iiiBB')
            header = data[:header_size]
            chunk_x, chunk_y, chunk_z, is_empty, is_full = struct.unpack(
                '<iiiBB', header
            )
            
            # Verify chunk position
            assert chunk_x == self.chunk_x
            assert chunk_y == self.chunk_y
            assert chunk_z == self.chunk_z
            
            self.is_empty = bool(is_empty)
            self.is_full = bool(is_full)
            
            if self.is_empty:
                self.blocks.fill(BlockType.AIR.value)
            else:
                # Decompress block data
                compressed_blocks = data[header_size:]
                block_bytes = zlib.decompress(compressed_blocks)
                self.blocks = np.frombuffer(block_bytes, dtype=np.uint8).reshape(
                    (CHUNK_SIZE, CHUNK_SIZE, CHUNK_SIZE)
                )
            
            self.is_dirty = True
    
    def get_bounding_box(self) -> AABB:
        """Get axis-aligned bounding box for the chunk."""
        min_point = Vec3(self.world_x, self.world_y, self.world_z)
        max_point = Vec3(
            self.world_x + CHUNK_SIZE,
            self.world_y + CHUNK_SIZE,
            self.world_z + CHUNK_SIZE
        )
        return AABB(min_point, max_point)
    
    def calculate_memory_usage(self) -> int:
        """Calculate memory usage in bytes."""
        memory = 0
        memory += self.blocks.nbytes  # Block data
        memory += self.light_data.nbytes  # Light data
        if self.vertex_data is not None:
            memory += self.vertex_data.nbytes  # Mesh data
        if self.compressed_data is not None:
            memory += len(self.compressed_data)  # Compressed data
        return memory


class ChunkManager:
    """
    Manages all chunks in the world.
    
    Handles loading, unloading, and accessing chunks efficiently.
    """
    
    def __init__(self, max_loaded_chunks: int = 1000):
        """
        Initialize chunk manager.
        
        Args:
            max_loaded_chunks: Maximum number of chunks to keep in memory
        """
        self.chunks: OrderedDict[Tuple[int, int, int], Chunk] = OrderedDict()
        self.max_loaded_chunks = max_loaded_chunks
        self.lock = threading.RLock()
        
        # Chunk loading queue
        self.load_queue: List[Tuple[int, int, int]] = []
        self.unload_queue: List[Tuple[int, int, int]] = []
        
        # Statistics
        self.chunks_loaded = 0
        self.chunks_unloaded = 0
        self.total_memory_usage = 0
    
    def get_chunk(self, chunk_x: int, chunk_y: int, chunk_z: int) -> Optional[Chunk]:
        """
        Get a chunk at chunk coordinates.
        
        Args:
            chunk_x, chunk_y, chunk_z: Chunk coordinates
            
        Returns:
            Chunk if loaded, None otherwise
        """
        with self.lock:
            key = (chunk_x, chunk_y, chunk_z)
            chunk = self.chunks.get(key)
            
            if chunk:
                # Move to end (LRU)
                self.chunks.move_to_end(key)
            
            return chunk
    
    def add_chunk(self, chunk: Chunk):
        """
        Add a chunk to the manager.
        
        Args:
            chunk: Chunk to add
        """
        with self.lock:
            key = (chunk.chunk_x, chunk.chunk_y, chunk.chunk_z)
            
            # Remove oldest chunks if at capacity
            while len(self.chunks) >= self.max_loaded_chunks:
                self._unload_oldest_chunk()
            
            self.chunks[key] = chunk
            self.chunks_loaded += 1
            self.total_memory_usage += chunk.calculate_memory_usage()
            
            # Update neighbor references
            self._update_chunk_neighbors(chunk)
    
    def remove_chunk(self, chunk_x: int, chunk_y: int, chunk_z: int) -> Optional[Chunk]:
        """
        Remove a chunk from the manager.
        
        Args:
            chunk_x, chunk_y, chunk_z: Chunk coordinates
            
        Returns:
            Removed chunk if it existed
        """
        with self.lock:
            key = (chunk_x, chunk_y, chunk_z)
            chunk = self.chunks.pop(key, None)
            
            if chunk:
                self.chunks_unloaded += 1
                self.total_memory_usage -= chunk.calculate_memory_usage()
                
                # Clear neighbor references
                for face, neighbor in chunk.neighbors.items():
                    if neighbor:
                        opposite_face = self._get_opposite_face(face)
                        neighbor.neighbors[opposite_face] = None
            
            return chunk
    
    def _update_chunk_neighbors(self, chunk: Chunk):
        """Update neighbor references for a chunk."""
        neighbor_offsets = {
            Face.TOP: (0, 1, 0),
            Face.BOTTOM: (0, -1, 0),
            Face.NORTH: (0, 0, -1),
            Face.SOUTH: (0, 0, 1),
            Face.EAST: (1, 0, 0),
            Face.WEST: (-1, 0, 0),
        }
        
        for face, (dx, dy, dz) in neighbor_offsets.items():
            neighbor_key = (
                chunk.chunk_x + dx,
                chunk.chunk_y + dy,
                chunk.chunk_z + dz
            )
            
            neighbor = self.chunks.get(neighbor_key)
            if neighbor:
                chunk.neighbors[face] = neighbor
                opposite_face = self._get_opposite_face(face)
                neighbor.neighbors[opposite_face] = chunk
    
    def _get_opposite_face(self, face: Face) -> Face:
        """Get the opposite face."""
        opposites = {
            Face.TOP: Face.BOTTOM,
            Face.BOTTOM: Face.TOP,
            Face.NORTH: Face.SOUTH,
            Face.SOUTH: Face.NORTH,
            Face.EAST: Face.WEST,
            Face.WEST: Face.EAST,
        }
        return opposites[face]
    
    def _unload_oldest_chunk(self):
        """Unload the least recently used chunk."""
        if self.chunks:
            oldest_key, oldest_chunk = self.chunks.popitem(last=False)
            self.unload_queue.append(oldest_key)
            self.chunks_unloaded += 1
            self.total_memory_usage -= oldest_chunk.calculate_memory_usage()
    
    def get_block_world(self, world_x: int, world_y: int, world_z: int) -> BlockType:
        """
        Get block at world coordinates.
        
        Args:
            world_x, world_y, world_z: World coordinates
            
        Returns:
            Block type at position
        """
        chunk_x = world_x // CHUNK_SIZE
        chunk_y = world_y // CHUNK_SIZE
        chunk_z = world_z // CHUNK_SIZE
        
        local_x = world_x % CHUNK_SIZE
        local_y = world_y % CHUNK_SIZE
        local_z = world_z % CHUNK_SIZE
        
        chunk = self.get_chunk(chunk_x, chunk_y, chunk_z)
        if chunk:
            return chunk.get_block(local_x, local_y, local_z)
        
        return BlockType.AIR
    
    def set_block_world(self, world_x: int, world_y: int, world_z: int,
                       block_type: BlockType) -> bool:
        """
        Set block at world coordinates.
        
        Args:
            world_x, world_y, world_z: World coordinates
            block_type: Type of block to set
            
        Returns:
            True if block was changed
        """
        chunk_x = world_x // CHUNK_SIZE
        chunk_y = world_y // CHUNK_SIZE
        chunk_z = world_z // CHUNK_SIZE
        
        local_x = world_x % CHUNK_SIZE
        local_y = world_y % CHUNK_SIZE
        local_z = world_z % CHUNK_SIZE
        
        chunk = self.get_chunk(chunk_x, chunk_y, chunk_z)
        if chunk:
            return chunk.set_block(local_x, local_y, local_z, block_type)
        
        return False
    
    def get_chunks_in_range(self, center_x: int, center_y: int, center_z: int,
                           radius: int) -> List[Chunk]:
        """
        Get all loaded chunks within range of a center point.
        
        Args:
            center_x, center_y, center_z: Center chunk coordinates
            radius: Radius in chunks
            
        Returns:
            List of chunks in range
        """
        chunks_in_range = []
        
        with self.lock:
            for x in range(center_x - radius, center_x + radius + 1):
                for y in range(max(0, center_y - radius), 
                             min(center_y + radius + 1, 16)):  # Limit Y range
                    for z in range(center_z - radius, center_z + radius + 1):
                        # Check if within sphere
                        dist_sq = ((x - center_x) ** 2 + 
                                 (y - center_y) ** 2 + 
                                 (z - center_z) ** 2)
                        
                        if dist_sq <= radius ** 2:
                            chunk = self.get_chunk(x, y, z)
                            if chunk:
                                chunks_in_range.append(chunk)
        
        return chunks_in_range
    
    def mark_dirty_around(self, world_x: int, world_y: int, world_z: int):
        """
        Mark chunks as dirty around a world position.
        
        Used when a block change might affect neighboring chunks.
        
        Args:
            world_x, world_y, world_z: World coordinates
        """
        # Get chunk coordinates
        chunk_x = world_x // CHUNK_SIZE
        chunk_y = world_y // CHUNK_SIZE
        chunk_z = world_z // CHUNK_SIZE
        
        # Get local coordinates
        local_x = world_x % CHUNK_SIZE
        local_y = world_y % CHUNK_SIZE
        local_z = world_z % CHUNK_SIZE
        
        # Mark main chunk as dirty
        main_chunk = self.get_chunk(chunk_x, chunk_y, chunk_z)
        if main_chunk:
            main_chunk.is_dirty = True
        
        # Check if on chunk boundary and mark neighbors
        if local_x == 0:
            neighbor = self.get_chunk(chunk_x - 1, chunk_y, chunk_z)
            if neighbor:
                neighbor.is_dirty = True
        elif local_x == CHUNK_SIZE - 1:
            neighbor = self.get_chunk(chunk_x + 1, chunk_y, chunk_z)
            if neighbor:
                neighbor.is_dirty = True
        
        if local_y == 0:
            neighbor = self.get_chunk(chunk_x, chunk_y - 1, chunk_z)
            if neighbor:
                neighbor.is_dirty = True
        elif local_y == CHUNK_SIZE - 1:
            neighbor = self.get_chunk(chunk_x, chunk_y + 1, chunk_z)
            if neighbor:
                neighbor.is_dirty = True
        
        if local_z == 0:
            neighbor = self.get_chunk(chunk_x, chunk_y, chunk_z - 1)
            if neighbor:
                neighbor.is_dirty = True
        elif local_z == CHUNK_SIZE - 1:
            neighbor = self.get_chunk(chunk_x, chunk_y, chunk_z + 1)
            if neighbor:
                neighbor.is_dirty = True
    
    def get_statistics(self) -> Dict[str, any]:
        """Get chunk manager statistics."""
        with self.lock:
            return {
                'chunks_loaded': len(self.chunks),
                'total_chunks_loaded': self.chunks_loaded,
                'total_chunks_unloaded': self.chunks_unloaded,
                'memory_usage_mb': self.total_memory_usage / (1024 * 1024),
                'load_queue_size': len(self.load_queue),
                'unload_queue_size': len(self.unload_queue),
            }